<?xml version="1.0" encoding="UTF-8" ?>
<testsuites name="vitest tests" tests="474" failures="31" errors="0" time="117.7730571">
    <testsuite name="src/arien.test.tsx" timestamp="2025-07-02T10:32:34.658Z" hostname="Ajayk" tests="1" failures="0" errors="0" skipped="0" time="0.0167829">
        <testcase classname="src/arien.test.tsx" name="gemini.tsx main function &gt; should call process.exit(1) if settings have errors" time="0.0135194">
        </testcase>
    </testsuite>
    <testsuite name="src/nonInteractiveCli.test.ts" timestamp="2025-07-02T10:32:34.660Z" hostname="Ajayk" tests="5" failures="0" errors="0" skipped="0" time="0.0383695">
        <testcase classname="src/nonInteractiveCli.test.ts" name="runNonInteractive &gt; should process input and write text output" time="0.0166125">
        </testcase>
        <testcase classname="src/nonInteractiveCli.test.ts" name="runNonInteractive &gt; should handle a single tool call and respond" time="0.0068792">
        </testcase>
        <testcase classname="src/nonInteractiveCli.test.ts" name="runNonInteractive &gt; should handle error during tool execution" time="0.0038487">
        </testcase>
        <testcase classname="src/nonInteractiveCli.test.ts" name="runNonInteractive &gt; should exit with error if sendMessageStream throws initially" time="0.0031366">
        </testcase>
        <testcase classname="src/nonInteractiveCli.test.ts" name="runNonInteractive &gt; should not exit if a tool is not found, and should send error back to model" time="0.0040114">
        </testcase>
    </testsuite>
    <testsuite name="src/config/built-in-mcp-servers.test.ts" timestamp="2025-07-02T10:32:34.665Z" hostname="Ajayk" tests="12" failures="7" errors="0" skipped="0" time="0.0677215">
        <testcase classname="src/config/built-in-mcp-servers.test.ts" name="Built-in MCP Servers &gt; getBuiltInMcpServers &gt; should return a copy of built-in MCP servers" time="0.0417932">
            <failure message="expected Promise{…} to deeply equal { …(7) }" type="AssertionError">
AssertionError: expected Promise{…} to deeply equal { …(7) }

- Expected
+ Received

- {
-   &quot;Context 7&quot;: MCPServerConfig {
-     &quot;args&quot;: [
-       &quot;-y&quot;,
-       &quot;@upstash/context7-mcp@latest&quot;,
-     ],
-     &quot;command&quot;: &quot;npx&quot;,
-     &quot;cwd&quot;: undefined,
-     &quot;description&quot;: &quot;Context7 MCP server for enhanced context management&quot;,
-     &quot;env&quot;: undefined,
-     &quot;httpUrl&quot;: undefined,
-     &quot;tcp&quot;: undefined,
-     &quot;timeout&quot;: undefined,
-     &quot;trust&quot;: undefined,
-     &quot;url&quot;: undefined,
-   },
-   &quot;Filesystem&quot;: MCPServerConfig {
-     &quot;args&quot;: [
-       &quot;-y&quot;,
-       &quot;@modelcontextprotocol/server-filesystem@latest&quot;,
-       &quot;--&quot;,
-       &quot;.&quot;,
-     ],
-     &quot;command&quot;: &quot;npx&quot;,
-     &quot;cwd&quot;: &quot;C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli&quot;,
-     &quot;description&quot;: &quot;Filesystem MCP server for file operations&quot;,
-     &quot;env&quot;: undefined,
-     &quot;httpUrl&quot;: undefined,
-     &quot;tcp&quot;: undefined,
-     &quot;timeout&quot;: 5000,
-     &quot;trust&quot;: undefined,
-     &quot;url&quot;: undefined,
-   },
-   &quot;Git&quot;: MCPServerConfig {
-     &quot;args&quot;: [
-       &quot;mcp-server-git&quot;,
-       &quot;--repository&quot;,
-       &quot;.&quot;,
-     ],
-     &quot;command&quot;: &quot;uvx&quot;,
-     &quot;cwd&quot;: &quot;C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli&quot;,
-     &quot;description&quot;: &quot;Git MCP server for version control operations&quot;,
-     &quot;env&quot;: undefined,
-     &quot;httpUrl&quot;: undefined,
-     &quot;tcp&quot;: undefined,
-     &quot;timeout&quot;: 10000,
-     &quot;trust&quot;: undefined,
-     &quot;url&quot;: undefined,
-   },
-   &quot;Memory&quot;: MCPServerConfig {
-     &quot;args&quot;: [
-       &quot;-y&quot;,
-       &quot;@modelcontextprotocol/server-memory&quot;,
-     ],
-     &quot;command&quot;: &quot;npx&quot;,
-     &quot;cwd&quot;: undefined,
-     &quot;description&quot;: &quot;Memory MCP server for persistent storage&quot;,
-     &quot;env&quot;: undefined,
-     &quot;httpUrl&quot;: undefined,
-     &quot;tcp&quot;: undefined,
-     &quot;timeout&quot;: 8000,
-     &quot;trust&quot;: undefined,
-     &quot;url&quot;: undefined,
-   },
-   &quot;Playwright&quot;: MCPServerConfig {
-     &quot;args&quot;: [
-       &quot;-y&quot;,
-       &quot;@playwright/mcp@latest&quot;,
-     ],
-     &quot;command&quot;: &quot;npx&quot;,
-     &quot;cwd&quot;: undefined,
-     &quot;description&quot;: &quot;Playwright MCP server for browser automation&quot;,
-     &quot;env&quot;: undefined,
-     &quot;httpUrl&quot;: undefined,
-     &quot;tcp&quot;: undefined,
-     &quot;timeout&quot;: undefined,
-     &quot;trust&quot;: undefined,
-     &quot;url&quot;: undefined,
-   },
-   &quot;Sequential Thinking&quot;: MCPServerConfig {
-     &quot;args&quot;: [
-       &quot;-y&quot;,
-       &quot;@modelcontextprotocol/server-sequential-thinking&quot;,
-     ],
-     &quot;command&quot;: &quot;npx&quot;,
-     &quot;cwd&quot;: undefined,
-     &quot;description&quot;: &quot;Sequential Thinking MCP server for dynamic problem-solving&quot;,
-     &quot;env&quot;: undefined,
-     &quot;httpUrl&quot;: undefined,
-     &quot;tcp&quot;: undefined,
-     &quot;timeout&quot;: undefined,
-     &quot;trust&quot;: undefined,
-     &quot;url&quot;: undefined,
-   },
-   &quot;Time&quot;: MCPServerConfig {
-     &quot;args&quot;: [
-       &quot;-y&quot;,
-       &quot;@modelcontextprotocol/server-time&quot;,
-     ],
-     &quot;command&quot;: &quot;npx&quot;,
-     &quot;cwd&quot;: undefined,
-     &quot;description&quot;: &quot;Time MCP server for date and time operations&quot;,
-     &quot;env&quot;: undefined,
-     &quot;httpUrl&quot;: undefined,
-     &quot;tcp&quot;: undefined,
-     &quot;timeout&quot;: 8000,
-     &quot;trust&quot;: undefined,
-     &quot;url&quot;: undefined,
-   },
- }
+ Promise {}

 ❯ src/config/built-in-mcp-servers.test.ts:25:23
            </failure>
        </testcase>
        <testcase classname="src/config/built-in-mcp-servers.test.ts" name="Built-in MCP Servers &gt; getBuiltInMcpServers &gt; should include expected built-in servers" time="0.0037481">
            <failure message="expected Promise{…} to have property &quot;Context 7&quot;" type="AssertionError">
AssertionError: expected Promise{…} to have property &quot;Context 7&quot;
 ❯ src/config/built-in-mcp-servers.test.ts:47:25
 ❯ src/config/built-in-mcp-servers.test.ts:46:23
            </failure>
        </testcase>
        <testcase classname="src/config/built-in-mcp-servers.test.ts" name="Built-in MCP Servers &gt; getBuiltInMcpServers &gt; should have valid configurations for all built-in servers" time="0.0026941">
        </testcase>
        <testcase classname="src/config/built-in-mcp-servers.test.ts" name="Built-in MCP Servers &gt; getConfigurableMcpServers &gt; should return a copy of configurable MCP servers" time="0.0018723">
        </testcase>
        <testcase classname="src/config/built-in-mcp-servers.test.ts" name="Built-in MCP Servers &gt; getConfigurableMcpServers &gt; should include expected configurable servers" time="0.0023168">
            <failure message="expected { …(1) } to have property &quot;Web Scraping AI&quot;" type="AssertionError">
AssertionError: expected { …(1) } to have property &quot;Web Scraping AI&quot;
 ❯ src/config/built-in-mcp-servers.test.ts:112:25
 ❯ src/config/built-in-mcp-servers.test.ts:111:23
            </failure>
        </testcase>
        <testcase classname="src/config/built-in-mcp-servers.test.ts" name="Built-in MCP Servers &gt; getConfigurableMcpServers &gt; should have environment variables for servers that require them" time="0.0028457">
            <failure message="Cannot read properties of undefined (reading &apos;env&apos;)" type="TypeError">
TypeError: Cannot read properties of undefined (reading &apos;env&apos;)
 ❯ src/config/built-in-mcp-servers.test.ts:143:36
 ❯ src/config/built-in-mcp-servers.test.ts:142:22
            </failure>
        </testcase>
        <testcase classname="src/config/built-in-mcp-servers.test.ts" name="Built-in MCP Servers &gt; getConfigurableMcpServers &gt; should have placeholder values for API keys" time="0.0012505">
            <failure message="Cannot read properties of undefined (reading &apos;env&apos;)" type="TypeError">
TypeError: Cannot read properties of undefined (reading &apos;env&apos;)
 ❯ src/config/built-in-mcp-servers.test.ts:154:32
            </failure>
        </testcase>
        <testcase classname="src/config/built-in-mcp-servers.test.ts" name="Built-in MCP Servers &gt; Server Configuration Validation &gt; should have valid transport configuration for built-in servers" time="0.0007379">
        </testcase>
        <testcase classname="src/config/built-in-mcp-servers.test.ts" name="Built-in MCP Servers &gt; Server Configuration Validation &gt; should have valid transport configuration for configurable servers" time="0.0006707">
        </testcase>
        <testcase classname="src/config/built-in-mcp-servers.test.ts" name="Built-in MCP Servers &gt; Server Configuration Validation &gt; should use appropriate package managers" time="0.0009332">
        </testcase>
        <testcase classname="src/config/built-in-mcp-servers.test.ts" name="Built-in MCP Servers &gt; Immutability &gt; should not allow modification of returned built-in servers" time="0.0019754">
            <failure message="expected Promise{…} to have property &quot;Context 7&quot;" type="AssertionError">
AssertionError: expected Promise{…} to have property &quot;Context 7&quot;
 ❯ src/config/built-in-mcp-servers.test.ts:209:24
            </failure>
        </testcase>
        <testcase classname="src/config/built-in-mcp-servers.test.ts" name="Built-in MCP Servers &gt; Immutability &gt; should not allow modification of returned configurable servers" time="0.001647">
            <failure message="expected { …(1) } to have property &quot;GitHub&quot;" type="AssertionError">
AssertionError: expected { …(1) } to have property &quot;GitHub&quot;
 ❯ src/config/built-in-mcp-servers.test.ts:220:24
            </failure>
        </testcase>
    </testsuite>
    <testsuite name="src/config/config.integration.test.ts" timestamp="2025-07-02T10:32:34.675Z" hostname="Ajayk" tests="11" failures="0" errors="0" skipped="0" time="0.3495012">
        <testcase classname="src/config/config.integration.test.ts" name="Configuration Integration Tests &gt; File Filtering Configuration &gt; should load default file filtering settings" time="0.1451357">
        </testcase>
        <testcase classname="src/config/config.integration.test.ts" name="Configuration Integration Tests &gt; File Filtering Configuration &gt; should load custom file filtering settings from configuration" time="0.0292098">
        </testcase>
        <testcase classname="src/config/config.integration.test.ts" name="Configuration Integration Tests &gt; File Filtering Configuration &gt; should merge user and workspace file filtering settings" time="0.0052037">
        </testcase>
        <testcase classname="src/config/config.integration.test.ts" name="Configuration Integration Tests &gt; Configuration Integration &gt; should handle partial configuration objects gracefully" time="0.0391709">
        </testcase>
        <testcase classname="src/config/config.integration.test.ts" name="Configuration Integration Tests &gt; Configuration Integration &gt; should handle empty configuration objects gracefully" time="0.0053303">
        </testcase>
        <testcase classname="src/config/config.integration.test.ts" name="Configuration Integration Tests &gt; Configuration Integration &gt; should handle missing configuration sections gracefully" time="0.0052398">
        </testcase>
        <testcase classname="src/config/config.integration.test.ts" name="Configuration Integration Tests &gt; Real-world Configuration Scenarios &gt; should handle a security-focused configuration" time="0.0418967">
        </testcase>
        <testcase classname="src/config/config.integration.test.ts" name="Configuration Integration Tests &gt; Real-world Configuration Scenarios &gt; should handle a CI/CD environment configuration" time="0.0343083">
        </testcase>
        <testcase classname="src/config/config.integration.test.ts" name="Configuration Integration Tests &gt; Checkpointing Configuration &gt; should enable checkpointing when the setting is true" time="0.0088612">
        </testcase>
        <testcase classname="src/config/config.integration.test.ts" name="Configuration Integration Tests &gt; Extension Context Files &gt; should have an empty array for extension context files by default" time="0.0064647">
        </testcase>
        <testcase classname="src/config/config.integration.test.ts" name="Configuration Integration Tests &gt; Extension Context Files &gt; should correctly store and return extension context file paths" time="0.0230714">
        </testcase>
    </testsuite>
    <testsuite name="src/config/config.test.ts" timestamp="2025-07-02T10:32:34.678Z" hostname="Ajayk" tests="28" failures="5" errors="0" skipped="0" time="83.6349882">
        <testcase classname="src/config/config.test.ts" name="loadCliConfig &gt; should set showMemoryUsage to true when --memory flag is present" time="5.0297457">
            <system-out>
✅ MCP server &apos;Context 7&apos; is compatible

✅ MCP server &apos;Playwright&apos; is compatible

            </system-out>
            <failure message="Test timed out in 5000ms.
If this is a long-running test, pass a timeout value as the last argument or configure it globally with &quot;testTimeout&quot;." type="Error">
Error: Test timed out in 5000ms.
If this is a long-running test, pass a timeout value as the last argument or configure it globally with &quot;testTimeout&quot;.
 ❯ src/config/config.test.ts:65:3
            </failure>
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig &gt; should set showMemoryUsage to false when --memory flag is not present" time="5.0272005">
            <system-out>
✅ MCP server &apos;Sequential Thinking&apos; is compatible
⚠️ MCP server &apos;Filesystem&apos; temporarily disabled due to known connection issues

✅ MCP server &apos;Git&apos; is compatible

✅ MCP server &apos;Context 7&apos; is compatible

✅ MCP server &apos;Memory&apos; is compatible

✅ MCP server &apos;Playwright&apos; is compatible

✅ MCP server &apos;Time&apos; is compatible

[DEBUG] Loading 6 validated built-in MCP servers.
[DEBUG] Adding MCP server &quot;Context 7&quot; from built-in.
[DEBUG] Adding MCP server &quot;Playwright&quot; from built-in.
[DEBUG] Adding MCP server &quot;Sequential Thinking&quot; from built-in.
[DEBUG] Adding MCP server &quot;Git&quot; from built-in.
[DEBUG] Adding MCP server &quot;Memory&quot; from built-in.
[DEBUG] Adding MCP server &quot;Time&quot; from built-in.
[DEBUG] Total MCP servers configured: 6

            </system-out>
            <failure message="Test timed out in 5000ms.
If this is a long-running test, pass a timeout value as the last argument or configure it globally with &quot;testTimeout&quot;." type="Error">
Error: Test timed out in 5000ms.
If this is a long-running test, pass a timeout value as the last argument or configure it globally with &quot;testTimeout&quot;.
 ❯ src/config/config.test.ts:72:3
            </failure>
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig &gt; should set showMemoryUsage to false by default from settings if CLI flag is not present" time="5.0051127">
            <system-out>
✅ MCP server &apos;Sequential Thinking&apos; is compatible
⚠️ MCP server &apos;Filesystem&apos; temporarily disabled due to known connection issues

✅ MCP server &apos;Git&apos; is compatible

✅ MCP server &apos;Context 7&apos; is compatible

✅ MCP server &apos;Memory&apos; is compatible

✅ MCP server &apos;Playwright&apos; is compatible

✅ MCP server &apos;Time&apos; is compatible

[DEBUG] Loading 6 validated built-in MCP servers.
[DEBUG] Adding MCP server &quot;Context 7&quot; from built-in.
[DEBUG] Adding MCP server &quot;Playwright&quot; from built-in.
[DEBUG] Adding MCP server &quot;Sequential Thinking&quot; from built-in.
[DEBUG] Adding MCP server &quot;Git&quot; from built-in.
[DEBUG] Adding MCP server &quot;Memory&quot; from built-in.
[DEBUG] Adding MCP server &quot;Time&quot; from built-in.
[DEBUG] Total MCP servers configured: 6

            </system-out>
            <failure message="Test timed out in 5000ms.
If this is a long-running test, pass a timeout value as the last argument or configure it globally with &quot;testTimeout&quot;." type="Error">
Error: Test timed out in 5000ms.
If this is a long-running test, pass a timeout value as the last argument or configure it globally with &quot;testTimeout&quot;.
 ❯ src/config/config.test.ts:79:3
            </failure>
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig &gt; should prioritize CLI flag over settings for showMemoryUsage (CLI true, settings false)" time="4.079638">
            <system-out>
✅ MCP server &apos;Sequential Thinking&apos; is compatible
⚠️ MCP server &apos;Filesystem&apos; temporarily disabled due to known connection issues

✅ MCP server &apos;Git&apos; is compatible

✅ MCP server &apos;Context 7&apos; is compatible

✅ MCP server &apos;Memory&apos; is compatible

✅ MCP server &apos;Playwright&apos; is compatible

✅ MCP server &apos;Time&apos; is compatible

[DEBUG] Loading 6 validated built-in MCP servers.
[DEBUG] Adding MCP server &quot;Context 7&quot; from built-in.
[DEBUG] Adding MCP server &quot;Playwright&quot; from built-in.
[DEBUG] Adding MCP server &quot;Sequential Thinking&quot; from built-in.
[DEBUG] Adding MCP server &quot;Git&quot; from built-in.
[DEBUG] Adding MCP server &quot;Memory&quot; from built-in.
[DEBUG] Adding MCP server &quot;Time&quot; from built-in.
[DEBUG] Total MCP servers configured: 6

✅ MCP server &apos;Sequential Thinking&apos; is compatible
⚠️ MCP server &apos;Filesystem&apos; temporarily disabled due to known connection issues

✅ MCP server &apos;Git&apos; is compatible

✅ MCP server &apos;Memory&apos; is compatible

✅ MCP server &apos;Time&apos; is compatible

[DEBUG] Loading 6 validated built-in MCP servers.
[DEBUG] Adding MCP server &quot;Context 7&quot; from built-in.
[DEBUG] Adding MCP server &quot;Playwright&quot; from built-in.
[DEBUG] Adding MCP server &quot;Sequential Thinking&quot; from built-in.
[DEBUG] Adding MCP server &quot;Git&quot; from built-in.
[DEBUG] Adding MCP server &quot;Memory&quot; from built-in.
[DEBUG] Adding MCP server &quot;Time&quot; from built-in.
[DEBUG] Total MCP servers configured: 6

            </system-out>
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should set telemetry to false by default when no flag or setting is present" time="2.9611219">
            <system-out>
✅ MCP server &apos;Context 7&apos; is compatible

✅ MCP server &apos;Playwright&apos; is compatible

✅ MCP server &apos;Sequential Thinking&apos; is compatible
⚠️ MCP server &apos;Filesystem&apos; temporarily disabled due to known connection issues

✅ MCP server &apos;Git&apos; is compatible

✅ MCP server &apos;Memory&apos; is compatible

✅ MCP server &apos;Time&apos; is compatible

[DEBUG] Loading 6 validated built-in MCP servers.
[DEBUG] Adding MCP server &quot;Context 7&quot; from built-in.
[DEBUG] Adding MCP server &quot;Playwright&quot; from built-in.
[DEBUG] Adding MCP server &quot;Sequential Thinking&quot; from built-in.
[DEBUG] Adding MCP server &quot;Git&quot; from built-in.
[DEBUG] Adding MCP server &quot;Memory&quot; from built-in.
[DEBUG] Adding MCP server &quot;Time&quot; from built-in.
[DEBUG] Total MCP servers configured: 6

            </system-out>
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should set telemetry to true when --telemetry flag is present" time="2.9226612">
            <system-out>
✅ MCP server &apos;Context 7&apos; is compatible

✅ MCP server &apos;Playwright&apos; is compatible

✅ MCP server &apos;Sequential Thinking&apos; is compatible
⚠️ MCP server &apos;Filesystem&apos; temporarily disabled due to known connection issues

✅ MCP server &apos;Git&apos; is compatible

✅ MCP server &apos;Memory&apos; is compatible

✅ MCP server &apos;Time&apos; is compatible

[DEBUG] Loading 6 validated built-in MCP servers.
[DEBUG] Adding MCP server &quot;Context 7&quot; from built-in.
[DEBUG] Adding MCP server &quot;Playwright&quot; from built-in.
[DEBUG] Adding MCP server &quot;Sequential Thinking&quot; from built-in.
[DEBUG] Adding MCP server &quot;Git&quot; from built-in.
[DEBUG] Adding MCP server &quot;Memory&quot; from built-in.
[DEBUG] Adding MCP server &quot;Time&quot; from built-in.
[DEBUG] Total MCP servers configured: 6

OpenTelemetry SDK started successfully.

            </system-out>
            <system-err>
Accessing resource attributes before async attributes settled

            </system-err>
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should set telemetry to false when --no-telemetry flag is present" time="2.8919157">
            <system-out>
✅ MCP server &apos;Context 7&apos; is compatible

✅ MCP server &apos;Playwright&apos; is compatible

✅ MCP server &apos;Sequential Thinking&apos; is compatible
⚠️ MCP server &apos;Filesystem&apos; temporarily disabled due to known connection issues

✅ MCP server &apos;Git&apos; is compatible

✅ MCP server &apos;Memory&apos; is compatible

✅ MCP server &apos;Time&apos; is compatible

[DEBUG] Loading 6 validated built-in MCP servers.
[DEBUG] Adding MCP server &quot;Context 7&quot; from built-in.
[DEBUG] Adding MCP server &quot;Playwright&quot; from built-in.
[DEBUG] Adding MCP server &quot;Sequential Thinking&quot; from built-in.
[DEBUG] Adding MCP server &quot;Git&quot; from built-in.
[DEBUG] Adding MCP server &quot;Memory&quot; from built-in.
[DEBUG] Adding MCP server &quot;Time&quot; from built-in.
[DEBUG] Total MCP servers configured: 6

            </system-out>
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should use telemetry value from settings if CLI flag is not present (settings true)" time="2.8875013">
            <system-out>
✅ MCP server &apos;Context 7&apos; is compatible

✅ MCP server &apos;Playwright&apos; is compatible

✅ MCP server &apos;Sequential Thinking&apos; is compatible
⚠️ MCP server &apos;Filesystem&apos; temporarily disabled due to known connection issues

✅ MCP server &apos;Git&apos; is compatible

✅ MCP server &apos;Memory&apos; is compatible

✅ MCP server &apos;Time&apos; is compatible

[DEBUG] Loading 6 validated built-in MCP servers.
[DEBUG] Adding MCP server &quot;Context 7&quot; from built-in.
[DEBUG] Adding MCP server &quot;Playwright&quot; from built-in.
[DEBUG] Adding MCP server &quot;Sequential Thinking&quot; from built-in.
[DEBUG] Adding MCP server &quot;Git&quot; from built-in.
[DEBUG] Adding MCP server &quot;Memory&quot; from built-in.
[DEBUG] Adding MCP server &quot;Time&quot; from built-in.
[DEBUG] Total MCP servers configured: 6

            </system-out>
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should use telemetry value from settings if CLI flag is not present (settings false)" time="2.8884086">
            <system-out>
✅ MCP server &apos;Context 7&apos; is compatible

✅ MCP server &apos;Playwright&apos; is compatible

✅ MCP server &apos;Sequential Thinking&apos; is compatible
⚠️ MCP server &apos;Filesystem&apos; temporarily disabled due to known connection issues

✅ MCP server &apos;Git&apos; is compatible

✅ MCP server &apos;Memory&apos; is compatible

✅ MCP server &apos;Time&apos; is compatible

[DEBUG] Loading 6 validated built-in MCP servers.
[DEBUG] Adding MCP server &quot;Context 7&quot; from built-in.
[DEBUG] Adding MCP server &quot;Playwright&quot; from built-in.
[DEBUG] Adding MCP server &quot;Sequential Thinking&quot; from built-in.
[DEBUG] Adding MCP server &quot;Git&quot; from built-in.
[DEBUG] Adding MCP server &quot;Memory&quot; from built-in.
[DEBUG] Adding MCP server &quot;Time&quot; from built-in.
[DEBUG] Total MCP servers configured: 6

            </system-out>
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should prioritize --telemetry CLI flag (true) over settings (false)" time="2.8504958">
            <system-out>
✅ MCP server &apos;Context 7&apos; is compatible

✅ MCP server &apos;Playwright&apos; is compatible

✅ MCP server &apos;Sequential Thinking&apos; is compatible
⚠️ MCP server &apos;Filesystem&apos; temporarily disabled due to known connection issues

✅ MCP server &apos;Git&apos; is compatible

✅ MCP server &apos;Memory&apos; is compatible

✅ MCP server &apos;Time&apos; is compatible

[DEBUG] Loading 6 validated built-in MCP servers.
[DEBUG] Adding MCP server &quot;Context 7&quot; from built-in.
[DEBUG] Adding MCP server &quot;Playwright&quot; from built-in.
[DEBUG] Adding MCP server &quot;Sequential Thinking&quot; from built-in.
[DEBUG] Adding MCP server &quot;Git&quot; from built-in.
[DEBUG] Adding MCP server &quot;Memory&quot; from built-in.
[DEBUG] Adding MCP server &quot;Time&quot; from built-in.
[DEBUG] Total MCP servers configured: 6

            </system-out>
            <system-err>
{&quot;stack&quot;:&quot;Error: PeriodicExportingMetricReader: metrics export failed (error Error: 14 UNAVAILABLE: No connection established. Last error: Error: connect ECONNREFUSED 127.0.0.1:4317)\n    at doExport (C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\node_modules\\@opentelemetry\\sdk-metrics\\src\\export\\PeriodicExportingMetricReader.ts:133:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at PeriodicExportingMetricReader._doRun (C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\node_modules\\@opentelemetry\\sdk-metrics\\src\\export\\PeriodicExportingMetricReader.ts:147:7)\n    at PeriodicExportingMetricReader._runOnce (C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\node_modules\\@opentelemetry\\sdk-metrics\\src\\export\\PeriodicExportingMetricReader.ts:104:7)&quot;,&quot;message&quot;:&quot;PeriodicExportingMetricReader: metrics export failed (error Error: 14 UNAVAILABLE: No connection established. Last error: Error: connect ECONNREFUSED 127.0.0.1:4317)&quot;,&quot;name&quot;:&quot;Error&quot;}

            </system-err>
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should prioritize --no-telemetry CLI flag (false) over settings (true)" time="2.8194443">
            <system-out>
✅ MCP server &apos;Context 7&apos; is compatible

✅ MCP server &apos;Playwright&apos; is compatible

✅ MCP server &apos;Sequential Thinking&apos; is compatible
⚠️ MCP server &apos;Filesystem&apos; temporarily disabled due to known connection issues

✅ MCP server &apos;Git&apos; is compatible

✅ MCP server &apos;Memory&apos; is compatible

✅ MCP server &apos;Time&apos; is compatible

[DEBUG] Loading 6 validated built-in MCP servers.
[DEBUG] Adding MCP server &quot;Context 7&quot; from built-in.
[DEBUG] Adding MCP server &quot;Playwright&quot; from built-in.
[DEBUG] Adding MCP server &quot;Sequential Thinking&quot; from built-in.
[DEBUG] Adding MCP server &quot;Git&quot; from built-in.
[DEBUG] Adding MCP server &quot;Memory&quot; from built-in.
[DEBUG] Adding MCP server &quot;Time&quot; from built-in.
[DEBUG] Total MCP servers configured: 6

            </system-out>
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should use telemetry OTLP endpoint from settings if CLI flag is not present" time="2.8537473">
            <system-out>
✅ MCP server &apos;Context 7&apos; is compatible

✅ MCP server &apos;Playwright&apos; is compatible

✅ MCP server &apos;Sequential Thinking&apos; is compatible
⚠️ MCP server &apos;Filesystem&apos; temporarily disabled due to known connection issues

✅ MCP server &apos;Git&apos; is compatible

✅ MCP server &apos;Memory&apos; is compatible

✅ MCP server &apos;Time&apos; is compatible

[DEBUG] Loading 6 validated built-in MCP servers.
[DEBUG] Adding MCP server &quot;Context 7&quot; from built-in.
[DEBUG] Adding MCP server &quot;Playwright&quot; from built-in.
[DEBUG] Adding MCP server &quot;Sequential Thinking&quot; from built-in.
[DEBUG] Adding MCP server &quot;Git&quot; from built-in.
[DEBUG] Adding MCP server &quot;Memory&quot; from built-in.
[DEBUG] Adding MCP server &quot;Time&quot; from built-in.
[DEBUG] Total MCP servers configured: 6

            </system-out>
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should prioritize --telemetry-otlp-endpoint CLI flag over settings" time="2.7903609">
            <system-out>
✅ MCP server &apos;Context 7&apos; is compatible

✅ MCP server &apos;Playwright&apos; is compatible

✅ MCP server &apos;Sequential Thinking&apos; is compatible
⚠️ MCP server &apos;Filesystem&apos; temporarily disabled due to known connection issues

✅ MCP server &apos;Git&apos; is compatible

✅ MCP server &apos;Memory&apos; is compatible

✅ MCP server &apos;Time&apos; is compatible

[DEBUG] Loading 6 validated built-in MCP servers.
[DEBUG] Adding MCP server &quot;Context 7&quot; from built-in.
[DEBUG] Adding MCP server &quot;Playwright&quot; from built-in.
[DEBUG] Adding MCP server &quot;Sequential Thinking&quot; from built-in.
[DEBUG] Adding MCP server &quot;Git&quot; from built-in.
[DEBUG] Adding MCP server &quot;Memory&quot; from built-in.
[DEBUG] Adding MCP server &quot;Time&quot; from built-in.
[DEBUG] Total MCP servers configured: 6

            </system-out>
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should use default endpoint if no OTLP endpoint is provided via CLI or settings" time="2.8101171">
            <system-out>
✅ MCP server &apos;Context 7&apos; is compatible

✅ MCP server &apos;Playwright&apos; is compatible

✅ MCP server &apos;Sequential Thinking&apos; is compatible
⚠️ MCP server &apos;Filesystem&apos; temporarily disabled due to known connection issues

✅ MCP server &apos;Git&apos; is compatible

✅ MCP server &apos;Memory&apos; is compatible

✅ MCP server &apos;Time&apos; is compatible

[DEBUG] Loading 6 validated built-in MCP servers.
[DEBUG] Adding MCP server &quot;Context 7&quot; from built-in.
[DEBUG] Adding MCP server &quot;Playwright&quot; from built-in.
[DEBUG] Adding MCP server &quot;Sequential Thinking&quot; from built-in.
[DEBUG] Adding MCP server &quot;Git&quot; from built-in.
[DEBUG] Adding MCP server &quot;Memory&quot; from built-in.
[DEBUG] Adding MCP server &quot;Time&quot; from built-in.
[DEBUG] Total MCP servers configured: 6

            </system-out>
            <system-err>
{&quot;stack&quot;:&quot;Error: PeriodicExportingMetricReader: metrics export failed (error Error: 14 UNAVAILABLE: No connection established. Last error: Error: connect ECONNREFUSED ::1:4317)\n    at doExport (C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\node_modules\\@opentelemetry\\sdk-metrics\\src\\export\\PeriodicExportingMetricReader.ts:133:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at PeriodicExportingMetricReader._doRun (C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\node_modules\\@opentelemetry\\sdk-metrics\\src\\export\\PeriodicExportingMetricReader.ts:147:7)\n    at PeriodicExportingMetricReader._runOnce (C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\node_modules\\@opentelemetry\\sdk-metrics\\src\\export\\PeriodicExportingMetricReader.ts:104:7)&quot;,&quot;message&quot;:&quot;PeriodicExportingMetricReader: metrics export failed (error Error: 14 UNAVAILABLE: No connection established. Last error: Error: connect ECONNREFUSED ::1:4317)&quot;,&quot;name&quot;:&quot;Error&quot;}

            </system-err>
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should use telemetry target from settings if CLI flag is not present" time="2.7768031">
            <system-out>
✅ MCP server &apos;Context 7&apos; is compatible

✅ MCP server &apos;Playwright&apos; is compatible

✅ MCP server &apos;Sequential Thinking&apos; is compatible
⚠️ MCP server &apos;Filesystem&apos; temporarily disabled due to known connection issues

✅ MCP server &apos;Git&apos; is compatible

✅ MCP server &apos;Memory&apos; is compatible

✅ MCP server &apos;Time&apos; is compatible

[DEBUG] Loading 6 validated built-in MCP servers.
[DEBUG] Adding MCP server &quot;Context 7&quot; from built-in.
[DEBUG] Adding MCP server &quot;Playwright&quot; from built-in.
[DEBUG] Adding MCP server &quot;Sequential Thinking&quot; from built-in.
[DEBUG] Adding MCP server &quot;Git&quot; from built-in.
[DEBUG] Adding MCP server &quot;Memory&quot; from built-in.
[DEBUG] Adding MCP server &quot;Time&quot; from built-in.
[DEBUG] Total MCP servers configured: 6

            </system-out>
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should prioritize --telemetry-target CLI flag over settings" time="2.8338868">
            <system-out>
✅ MCP server &apos;Context 7&apos; is compatible

✅ MCP server &apos;Playwright&apos; is compatible

✅ MCP server &apos;Sequential Thinking&apos; is compatible
⚠️ MCP server &apos;Filesystem&apos; temporarily disabled due to known connection issues

✅ MCP server &apos;Git&apos; is compatible

✅ MCP server &apos;Memory&apos; is compatible

✅ MCP server &apos;Time&apos; is compatible

[DEBUG] Loading 6 validated built-in MCP servers.
[DEBUG] Adding MCP server &quot;Context 7&quot; from built-in.
[DEBUG] Adding MCP server &quot;Playwright&quot; from built-in.
[DEBUG] Adding MCP server &quot;Sequential Thinking&quot; from built-in.
[DEBUG] Adding MCP server &quot;Git&quot; from built-in.
[DEBUG] Adding MCP server &quot;Memory&quot; from built-in.
[DEBUG] Adding MCP server &quot;Time&quot; from built-in.
[DEBUG] Total MCP servers configured: 6

            </system-out>
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should use default target if no target is provided via CLI or settings" time="2.8239049">
            <system-out>
✅ MCP server &apos;Context 7&apos; is compatible

✅ MCP server &apos;Playwright&apos; is compatible

✅ MCP server &apos;Sequential Thinking&apos; is compatible
⚠️ MCP server &apos;Filesystem&apos; temporarily disabled due to known connection issues

✅ MCP server &apos;Git&apos; is compatible

✅ MCP server &apos;Memory&apos; is compatible

✅ MCP server &apos;Time&apos; is compatible

[DEBUG] Loading 6 validated built-in MCP servers.
[DEBUG] Adding MCP server &quot;Context 7&quot; from built-in.
[DEBUG] Adding MCP server &quot;Playwright&quot; from built-in.
[DEBUG] Adding MCP server &quot;Sequential Thinking&quot; from built-in.
[DEBUG] Adding MCP server &quot;Git&quot; from built-in.
[DEBUG] Adding MCP server &quot;Memory&quot; from built-in.
[DEBUG] Adding MCP server &quot;Time&quot; from built-in.
[DEBUG] Total MCP servers configured: 6

            </system-out>
            <system-err>
{&quot;stack&quot;:&quot;Error: PeriodicExportingMetricReader: metrics export failed (error Error: 14 UNAVAILABLE: No connection established. Last error: Error: connect ECONNREFUSED ::1:4317)\n    at doExport (C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\node_modules\\@opentelemetry\\sdk-metrics\\src\\export\\PeriodicExportingMetricReader.ts:133:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at PeriodicExportingMetricReader._doRun (C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\node_modules\\@opentelemetry\\sdk-metrics\\src\\export\\PeriodicExportingMetricReader.ts:147:7)\n    at PeriodicExportingMetricReader._runOnce (C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\node_modules\\@opentelemetry\\sdk-metrics\\src\\export\\PeriodicExportingMetricReader.ts:104:7)&quot;,&quot;message&quot;:&quot;PeriodicExportingMetricReader: metrics export failed (error Error: 14 UNAVAILABLE: No connection established. Last error: Error: connect ECONNREFUSED ::1:4317)&quot;,&quot;name&quot;:&quot;Error&quot;}

            </system-err>
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should use telemetry log prompts from settings if CLI flag is not present" time="2.9815265">
            <system-out>
✅ MCP server &apos;Context 7&apos; is compatible

✅ MCP server &apos;Playwright&apos; is compatible

✅ MCP server &apos;Sequential Thinking&apos; is compatible
⚠️ MCP server &apos;Filesystem&apos; temporarily disabled due to known connection issues

✅ MCP server &apos;Git&apos; is compatible

✅ MCP server &apos;Memory&apos; is compatible

✅ MCP server &apos;Time&apos; is compatible

[DEBUG] Loading 6 validated built-in MCP servers.
[DEBUG] Adding MCP server &quot;Context 7&quot; from built-in.
[DEBUG] Adding MCP server &quot;Playwright&quot; from built-in.
[DEBUG] Adding MCP server &quot;Sequential Thinking&quot; from built-in.
[DEBUG] Adding MCP server &quot;Git&quot; from built-in.
[DEBUG] Adding MCP server &quot;Memory&quot; from built-in.
[DEBUG] Adding MCP server &quot;Time&quot; from built-in.
[DEBUG] Total MCP servers configured: 6

            </system-out>
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should prioritize --telemetry-log-prompts CLI flag (true) over settings (false)" time="2.8236585">
            <system-out>
✅ MCP server &apos;Context 7&apos; is compatible

✅ MCP server &apos;Playwright&apos; is compatible

✅ MCP server &apos;Sequential Thinking&apos; is compatible
⚠️ MCP server &apos;Filesystem&apos; temporarily disabled due to known connection issues

✅ MCP server &apos;Git&apos; is compatible

✅ MCP server &apos;Memory&apos; is compatible

✅ MCP server &apos;Time&apos; is compatible

[DEBUG] Loading 6 validated built-in MCP servers.
[DEBUG] Adding MCP server &quot;Context 7&quot; from built-in.
[DEBUG] Adding MCP server &quot;Playwright&quot; from built-in.
[DEBUG] Adding MCP server &quot;Sequential Thinking&quot; from built-in.
[DEBUG] Adding MCP server &quot;Git&quot; from built-in.
[DEBUG] Adding MCP server &quot;Memory&quot; from built-in.
[DEBUG] Adding MCP server &quot;Time&quot; from built-in.
[DEBUG] Total MCP servers configured: 6

            </system-out>
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should prioritize --no-telemetry-log-prompts CLI flag (false) over settings (true)" time="2.7840645">
            <system-out>
✅ MCP server &apos;Context 7&apos; is compatible

✅ MCP server &apos;Playwright&apos; is compatible

✅ MCP server &apos;Sequential Thinking&apos; is compatible
⚠️ MCP server &apos;Filesystem&apos; temporarily disabled due to known connection issues

✅ MCP server &apos;Git&apos; is compatible

✅ MCP server &apos;Memory&apos; is compatible

✅ MCP server &apos;Time&apos; is compatible

[DEBUG] Loading 6 validated built-in MCP servers.
[DEBUG] Adding MCP server &quot;Context 7&quot; from built-in.
[DEBUG] Adding MCP server &quot;Playwright&quot; from built-in.
[DEBUG] Adding MCP server &quot;Sequential Thinking&quot; from built-in.
[DEBUG] Adding MCP server &quot;Git&quot; from built-in.
[DEBUG] Adding MCP server &quot;Memory&quot; from built-in.
[DEBUG] Adding MCP server &quot;Time&quot; from built-in.
[DEBUG] Total MCP servers configured: 6

            </system-out>
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should use default log prompts (true) if no value is provided via CLI or settings" time="2.7505804">
            <system-out>
✅ MCP server &apos;Context 7&apos; is compatible

✅ MCP server &apos;Playwright&apos; is compatible

✅ MCP server &apos;Sequential Thinking&apos; is compatible
⚠️ MCP server &apos;Filesystem&apos; temporarily disabled due to known connection issues

✅ MCP server &apos;Git&apos; is compatible

✅ MCP server &apos;Memory&apos; is compatible

✅ MCP server &apos;Time&apos; is compatible

[DEBUG] Loading 6 validated built-in MCP servers.
[DEBUG] Adding MCP server &quot;Context 7&quot; from built-in.
[DEBUG] Adding MCP server &quot;Playwright&quot; from built-in.
[DEBUG] Adding MCP server &quot;Sequential Thinking&quot; from built-in.
[DEBUG] Adding MCP server &quot;Git&quot; from built-in.
[DEBUG] Adding MCP server &quot;Memory&quot; from built-in.
[DEBUG] Adding MCP server &quot;Time&quot; from built-in.
[DEBUG] Total MCP servers configured: 6

            </system-out>
            <system-err>
{&quot;stack&quot;:&quot;Error: PeriodicExportingMetricReader: metrics export failed (error Error: 14 UNAVAILABLE: No connection established. Last error: Error: connect ECONNREFUSED ::1:4317)\n    at doExport (C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\node_modules\\@opentelemetry\\sdk-metrics\\src\\export\\PeriodicExportingMetricReader.ts:133:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at PeriodicExportingMetricReader._doRun (C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\node_modules\\@opentelemetry\\sdk-metrics\\src\\export\\PeriodicExportingMetricReader.ts:147:7)\n    at PeriodicExportingMetricReader._runOnce (C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\node_modules\\@opentelemetry\\sdk-metrics\\src\\export\\PeriodicExportingMetricReader.ts:104:7)&quot;,&quot;message&quot;:&quot;PeriodicExportingMetricReader: metrics export failed (error Error: 14 UNAVAILABLE: No connection established. Last error: Error: connect ECONNREFUSED ::1:4317)&quot;,&quot;name&quot;:&quot;Error&quot;}

            </system-err>
        </testcase>
        <testcase classname="src/config/config.test.ts" name="Hierarchical Memory Loading (config.ts) - Placeholder Suite &gt; should pass extension context file paths to loadServerHierarchicalMemory" time="2.6707594">
            <system-out>
✅ MCP server &apos;Context 7&apos; is compatible

✅ MCP server &apos;Playwright&apos; is compatible

✅ MCP server &apos;Sequential Thinking&apos; is compatible
⚠️ MCP server &apos;Filesystem&apos; temporarily disabled due to known connection issues

✅ MCP server &apos;Git&apos; is compatible

✅ MCP server &apos;Memory&apos; is compatible

✅ MCP server &apos;Time&apos; is compatible

[DEBUG] Loading 6 validated built-in MCP servers.
[DEBUG] Adding MCP server &quot;Context 7&quot; from built-in.
[DEBUG] Adding MCP server &quot;Playwright&quot; from built-in.
[DEBUG] Adding MCP server &quot;Sequential Thinking&quot; from built-in.
[DEBUG] Adding MCP server &quot;Git&quot; from built-in.
[DEBUG] Adding MCP server &quot;Memory&quot; from built-in.
[DEBUG] Adding MCP server &quot;Time&quot; from built-in.
[DEBUG] Total MCP servers configured: 6

            </system-out>
        </testcase>
        <testcase classname="src/config/config.test.ts" name="mergeMcpServers &gt; should not modify the original settings object" time="2.688533">
            <system-out>
✅ MCP server &apos;Context 7&apos; is compatible

✅ MCP server &apos;Playwright&apos; is compatible

✅ MCP server &apos;Sequential Thinking&apos; is compatible
⚠️ MCP server &apos;Filesystem&apos; temporarily disabled due to known connection issues

✅ MCP server &apos;Git&apos; is compatible

✅ MCP server &apos;Memory&apos; is compatible

✅ MCP server &apos;Time&apos; is compatible

[DEBUG] Loading 6 validated built-in MCP servers.
[DEBUG] Adding MCP server &quot;Context 7&quot; from built-in.
[DEBUG] Adding MCP server &quot;Playwright&quot; from built-in.
[DEBUG] Adding MCP server &quot;Sequential Thinking&quot; from built-in.
[DEBUG] Adding MCP server &quot;Git&quot; from built-in.
[DEBUG] Adding MCP server &quot;Memory&quot; from built-in.
[DEBUG] Adding MCP server &quot;Time&quot; from built-in.
[DEBUG] Loading 1 MCP servers from extension &quot;ext1&quot;.
[DEBUG] Adding MCP server &quot;ext1-server&quot; from extension &quot;ext1&quot;.
[DEBUG] Loading 1 MCP servers from user settings.
[DEBUG] Adding MCP server &quot;test-server&quot; from user settings.
[DEBUG] Total MCP servers configured: 8

            </system-out>
        </testcase>
        <testcase classname="src/config/config.test.ts" name="mergeMcpServers &gt; should include built-in MCP servers by default" time="2.6655077">
            <system-out>
✅ MCP server &apos;Context 7&apos; is compatible

✅ MCP server &apos;Playwright&apos; is compatible

✅ MCP server &apos;Sequential Thinking&apos; is compatible
⚠️ MCP server &apos;Filesystem&apos; temporarily disabled due to known connection issues

✅ MCP server &apos;Git&apos; is compatible

✅ MCP server &apos;Memory&apos; is compatible

✅ MCP server &apos;Time&apos; is compatible

[DEBUG] Loading 6 validated built-in MCP servers.
[DEBUG] Adding MCP server &quot;Context 7&quot; from built-in.
[DEBUG] Adding MCP server &quot;Playwright&quot; from built-in.
[DEBUG] Adding MCP server &quot;Sequential Thinking&quot; from built-in.
[DEBUG] Adding MCP server &quot;Git&quot; from built-in.
[DEBUG] Adding MCP server &quot;Memory&quot; from built-in.
[DEBUG] Adding MCP server &quot;Time&quot; from built-in.
[DEBUG] Total MCP servers configured: 6

            </system-out>
            <system-err>
{&quot;stack&quot;:&quot;Error: PeriodicExportingMetricReader: metrics export failed (error Error: 14 UNAVAILABLE: No connection established. Last error: Error: connect ECONNREFUSED ::1:4317)\n    at doExport (C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\node_modules\\@opentelemetry\\sdk-metrics\\src\\export\\PeriodicExportingMetricReader.ts:133:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at PeriodicExportingMetricReader._doRun (C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\node_modules\\@opentelemetry\\sdk-metrics\\src\\export\\PeriodicExportingMetricReader.ts:147:7)\n    at PeriodicExportingMetricReader._runOnce (C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\node_modules\\@opentelemetry\\sdk-metrics\\src\\export\\PeriodicExportingMetricReader.ts:104:7)&quot;,&quot;message&quot;:&quot;PeriodicExportingMetricReader: metrics export failed (error Error: 14 UNAVAILABLE: No connection established. Last error: Error: connect ECONNREFUSED ::1:4317)&quot;,&quot;name&quot;:&quot;Error&quot;}

            </system-err>
            <failure message="expected { …(6) } to have property &quot;Filesystem&quot;" type="AssertionError">
AssertionError: expected { …(6) } to have property &quot;Filesystem&quot;
 ❯ src/config/config.test.ts:362:24
            </failure>
        </testcase>
        <testcase classname="src/config/config.test.ts" name="mergeMcpServers &gt; should allow disabling built-in MCP servers" time="0.0073132">
            <system-out>
[DEBUG] Built-in MCP servers are disabled by user configuration.
[DEBUG] Total MCP servers configured: 0

            </system-out>
        </testcase>
        <testcase classname="src/config/config.test.ts" name="mergeMcpServers &gt; should allow user settings to override built-in MCP servers" time="2.6725126">
            <system-out>
✅ MCP server &apos;Context 7&apos; is compatible

✅ MCP server &apos;Playwright&apos; is compatible

✅ MCP server &apos;Sequential Thinking&apos; is compatible
⚠️ MCP server &apos;Filesystem&apos; temporarily disabled due to known connection issues

✅ MCP server &apos;Git&apos; is compatible

✅ MCP server &apos;Memory&apos; is compatible

✅ MCP server &apos;Time&apos; is compatible

[DEBUG] Loading 6 validated built-in MCP servers.
[DEBUG] Adding MCP server &quot;Context 7&quot; from built-in.
[DEBUG] Adding MCP server &quot;Playwright&quot; from built-in.
[DEBUG] Adding MCP server &quot;Sequential Thinking&quot; from built-in.
[DEBUG] Adding MCP server &quot;Git&quot; from built-in.
[DEBUG] Adding MCP server &quot;Memory&quot; from built-in.
[DEBUG] Adding MCP server &quot;Time&quot; from built-in.
[DEBUG] Loading 1 MCP servers from user settings.
[DEBUG] Overriding MCP server &quot;Context 7&quot; with user settings configuration.
[DEBUG] Total MCP servers configured: 6

            </system-out>
            <failure message="expected { &apos;Context 7&apos;: { …(2) }, …(5) } to have property &quot;Filesystem&quot;" type="AssertionError">
AssertionError: expected { &apos;Context 7&apos;: { …(2) }, …(5) } to have property &quot;Filesystem&quot;
 ❯ src/config/config.test.ts:408:24
            </failure>
        </testcase>
        <testcase classname="src/config/config.test.ts" name="mergeMcpServers &gt; should allow extension MCP servers to override built-in servers" time="2.6658846">
            <system-out>
✅ MCP server &apos;Context 7&apos; is compatible

✅ MCP server &apos;Playwright&apos; is compatible

✅ MCP server &apos;Sequential Thinking&apos; is compatible
⚠️ MCP server &apos;Filesystem&apos; temporarily disabled due to known connection issues

✅ MCP server &apos;Git&apos; is compatible

✅ MCP server &apos;Memory&apos; is compatible

✅ MCP server &apos;Time&apos; is compatible

[DEBUG] Loading 6 validated built-in MCP servers.
[DEBUG] Adding MCP server &quot;Context 7&quot; from built-in.
[DEBUG] Adding MCP server &quot;Playwright&quot; from built-in.
[DEBUG] Adding MCP server &quot;Sequential Thinking&quot; from built-in.
[DEBUG] Adding MCP server &quot;Git&quot; from built-in.
[DEBUG] Adding MCP server &quot;Memory&quot; from built-in.
[DEBUG] Adding MCP server &quot;Time&quot; from built-in.
[DEBUG] Loading 1 MCP servers from extension &quot;test-ext&quot;.
[DEBUG] Overriding MCP server &quot;Git&quot; with extension &quot;test-ext&quot; configuration.
[DEBUG] Total MCP servers configured: 6

            </system-out>
        </testcase>
        <testcase classname="src/config/config.test.ts" name="mergeMcpServers &gt; should maintain priority order: user &gt; extension &gt; built-in" time="2.6672198">
            <system-out>
✅ MCP server &apos;Context 7&apos; is compatible

✅ MCP server &apos;Playwright&apos; is compatible

✅ MCP server &apos;Sequential Thinking&apos; is compatible
⚠️ MCP server &apos;Filesystem&apos; temporarily disabled due to known connection issues

✅ MCP server &apos;Git&apos; is compatible

✅ MCP server &apos;Memory&apos; is compatible

✅ MCP server &apos;Time&apos; is compatible

[DEBUG] Loading 6 validated built-in MCP servers.
[DEBUG] Adding MCP server &quot;Context 7&quot; from built-in.
[DEBUG] Adding MCP server &quot;Playwright&quot; from built-in.
[DEBUG] Adding MCP server &quot;Sequential Thinking&quot; from built-in.
[DEBUG] Adding MCP server &quot;Git&quot; from built-in.
[DEBUG] Adding MCP server &quot;Memory&quot; from built-in.
[DEBUG] Adding MCP server &quot;Time&quot; from built-in.
[DEBUG] Loading 2 MCP servers from extension &quot;test-ext&quot;.
[DEBUG] Adding MCP server &quot;Calculator&quot; from extension &quot;test-ext&quot;.
[DEBUG] Overriding MCP server &quot;Git&quot; with extension &quot;test-ext&quot; configuration.
[DEBUG] Loading 1 MCP servers from user settings.
[DEBUG] Overriding MCP server &quot;Calculator&quot; with user settings configuration.
[DEBUG] Total MCP servers configured: 7

            </system-out>
        </testcase>
    </testsuite>
    <testsuite name="src/config/extension.test.ts" timestamp="2025-07-02T10:32:34.709Z" hostname="Ajayk" tests="2" failures="0" errors="0" skipped="0" time="0.0537519">
        <testcase classname="src/config/extension.test.ts" name="loadExtensions &gt; should load context file path when ARIEN.md is present" time="0.0323048">
            <system-out>
Loading extension: ext1 (version: 1.0.0)
Loading extension: ext2 (version: 2.0.0)

            </system-out>
        </testcase>
        <testcase classname="src/config/extension.test.ts" name="loadExtensions &gt; should load context file path from the extension config" time="0.0175846">
            <system-out>
Loading extension: ext1 (version: 1.0.0)

            </system-out>
        </testcase>
    </testsuite>
    <testsuite name="src/config/mcp-integration.test.ts" timestamp="2025-07-02T10:32:34.711Z" hostname="Ajayk" tests="6" failures="4" errors="0" skipped="0" time="21.3056416">
        <testcase classname="src/config/mcp-integration.test.ts" name="MCP Integration Tests &gt; Built-in MCP Server Integration &gt; should automatically load built-in MCP servers without any configuration" time="5.0296017">
            <system-out>
✅ MCP server &apos;Context 7&apos; is compatible

✅ MCP server &apos;Playwright&apos; is compatible

            </system-out>
            <failure message="Test timed out in 5000ms.
If this is a long-running test, pass a timeout value as the last argument or configure it globally with &quot;testTimeout&quot;." type="Error">
Error: Test timed out in 5000ms.
If this is a long-running test, pass a timeout value as the last argument or configure it globally with &quot;testTimeout&quot;.
 ❯ src/config/mcp-integration.test.ts:54:5
            </failure>
        </testcase>
        <testcase classname="src/config/mcp-integration.test.ts" name="MCP Integration Tests &gt; Built-in MCP Server Integration &gt; should respect the enableBuiltInMcpServers setting when disabled" time="0.2292281">
            <system-out>
[DEBUG] Built-in MCP servers are disabled by user configuration.
[DEBUG] Total MCP servers configured: 0

            </system-out>
        </testcase>
        <testcase classname="src/config/mcp-integration.test.ts" name="MCP Integration Tests &gt; Built-in MCP Server Integration &gt; should allow user configuration to override built-in servers" time="5.0113644">
            <system-out>
✅ MCP server &apos;Sequential Thinking&apos; is compatible
⚠️ MCP server &apos;Filesystem&apos; temporarily disabled due to known connection issues

✅ MCP server &apos;Git&apos; is compatible

✅ MCP server &apos;Context 7&apos; is compatible

✅ MCP server &apos;Memory&apos; is compatible

✅ MCP server &apos;Playwright&apos; is compatible

✅ MCP server &apos;Time&apos; is compatible

[DEBUG] Loading 6 validated built-in MCP servers.
[DEBUG] Adding MCP server &quot;Context 7&quot; from built-in.
[DEBUG] Adding MCP server &quot;Playwright&quot; from built-in.
[DEBUG] Adding MCP server &quot;Sequential Thinking&quot; from built-in.
[DEBUG] Adding MCP server &quot;Git&quot; from built-in.
[DEBUG] Adding MCP server &quot;Memory&quot; from built-in.
[DEBUG] Adding MCP server &quot;Time&quot; from built-in.
[DEBUG] Total MCP servers configured: 6

            </system-out>
            <failure message="Test timed out in 5000ms.
If this is a long-running test, pass a timeout value as the last argument or configure it globally with &quot;testTimeout&quot;." type="Error">
Error: Test timed out in 5000ms.
If this is a long-running test, pass a timeout value as the last argument or configure it globally with &quot;testTimeout&quot;.
 ❯ src/config/mcp-integration.test.ts:93:5
            </failure>
        </testcase>
        <testcase classname="src/config/mcp-integration.test.ts" name="MCP Integration Tests &gt; Built-in MCP Server Integration &gt; should allow extension configuration to override built-in servers" time="5.0043173">
            <system-out>
✅ MCP server &apos;Sequential Thinking&apos; is compatible
⚠️ MCP server &apos;Filesystem&apos; temporarily disabled due to known connection issues

✅ MCP server &apos;Git&apos; is compatible

✅ MCP server &apos;Context 7&apos; is compatible

✅ MCP server &apos;Memory&apos; is compatible

✅ MCP server &apos;Playwright&apos; is compatible

✅ MCP server &apos;Time&apos; is compatible

[DEBUG] Loading 6 validated built-in MCP servers.
[DEBUG] Adding MCP server &quot;Context 7&quot; from built-in.
[DEBUG] Adding MCP server &quot;Playwright&quot; from built-in.
[DEBUG] Adding MCP server &quot;Sequential Thinking&quot; from built-in.
[DEBUG] Adding MCP server &quot;Git&quot; from built-in.
[DEBUG] Adding MCP server &quot;Memory&quot; from built-in.
[DEBUG] Adding MCP server &quot;Time&quot; from built-in.
[DEBUG] Loading 2 MCP servers from user settings.
[DEBUG] Overriding MCP server &quot;Context 7&quot; with user settings configuration.
[DEBUG] Adding MCP server &quot;Custom Server&quot; from user settings.
[DEBUG] Total MCP servers configured: 7

✅ MCP server &apos;Sequential Thinking&apos; is compatible
⚠️ MCP server &apos;Filesystem&apos; temporarily disabled due to known connection issues

✅ MCP server &apos;Git&apos; is compatible

✅ MCP server &apos;Memory&apos; is compatible

            </system-out>
            <failure message="Test timed out in 5000ms.
If this is a long-running test, pass a timeout value as the last argument or configure it globally with &quot;testTimeout&quot;." type="Error">
Error: Test timed out in 5000ms.
If this is a long-running test, pass a timeout value as the last argument or configure it globally with &quot;testTimeout&quot;.
 ❯ src/config/mcp-integration.test.ts:128:5
            </failure>
        </testcase>
        <testcase classname="src/config/mcp-integration.test.ts" name="MCP Integration Tests &gt; Built-in MCP Server Integration &gt; should maintain correct priority: user &gt; extension &gt; built-in" time="3.094888">
            <system-out>
✅ MCP server &apos;Time&apos; is compatible

[DEBUG] Loading 6 validated built-in MCP servers.
[DEBUG] Adding MCP server &quot;Context 7&quot; from built-in.
[DEBUG] Adding MCP server &quot;Playwright&quot; from built-in.
[DEBUG] Adding MCP server &quot;Sequential Thinking&quot; from built-in.
[DEBUG] Adding MCP server &quot;Git&quot; from built-in.
[DEBUG] Adding MCP server &quot;Memory&quot; from built-in.
[DEBUG] Adding MCP server &quot;Time&quot; from built-in.
[DEBUG] Loading 2 MCP servers from extension &quot;test-extension&quot;.
[DEBUG] Overriding MCP server &quot;Git&quot; with extension &quot;test-extension&quot; configuration.
[DEBUG] Adding MCP server &quot;Extension Server&quot; from extension &quot;test-extension&quot;.
[DEBUG] Total MCP servers configured: 7

✅ MCP server &apos;Context 7&apos; is compatible

✅ MCP server &apos;Playwright&apos; is compatible

✅ MCP server &apos;Sequential Thinking&apos; is compatible
⚠️ MCP server &apos;Filesystem&apos; temporarily disabled due to known connection issues

✅ MCP server &apos;Git&apos; is compatible

✅ MCP server &apos;Memory&apos; is compatible

✅ MCP server &apos;Time&apos; is compatible

[DEBUG] Loading 6 validated built-in MCP servers.
[DEBUG] Adding MCP server &quot;Context 7&quot; from built-in.
[DEBUG] Adding MCP server &quot;Playwright&quot; from built-in.
[DEBUG] Adding MCP server &quot;Sequential Thinking&quot; from built-in.
[DEBUG] Adding MCP server &quot;Git&quot; from built-in.
[DEBUG] Adding MCP server &quot;Memory&quot; from built-in.
[DEBUG] Adding MCP server &quot;Time&quot; from built-in.
[DEBUG] Loading 2 MCP servers from extension &quot;test-extension&quot;.
[DEBUG] Adding MCP server &quot;Calculator&quot; from extension &quot;test-extension&quot;.
[DEBUG] Overriding MCP server &quot;Memory&quot; with extension &quot;test-extension&quot; configuration.
[DEBUG] Loading 1 MCP servers from user settings.
[DEBUG] Overriding MCP server &quot;Calculator&quot; with user settings configuration.
[DEBUG] Total MCP servers configured: 7

            </system-out>
        </testcase>
        <testcase classname="src/config/mcp-integration.test.ts" name="MCP Integration Tests &gt; Built-in MCP Server Integration &gt; should handle mixed configuration scenarios gracefully" time="2.9322618">
            <system-out>
✅ MCP server &apos;Context 7&apos; is compatible

✅ MCP server &apos;Playwright&apos; is compatible

✅ MCP server &apos;Sequential Thinking&apos; is compatible
⚠️ MCP server &apos;Filesystem&apos; temporarily disabled due to known connection issues

✅ MCP server &apos;Git&apos; is compatible

✅ MCP server &apos;Memory&apos; is compatible

✅ MCP server &apos;Time&apos; is compatible

[DEBUG] Loading 6 validated built-in MCP servers.
[DEBUG] Adding MCP server &quot;Context 7&quot; from built-in.
[DEBUG] Adding MCP server &quot;Playwright&quot; from built-in.
[DEBUG] Adding MCP server &quot;Sequential Thinking&quot; from built-in.
[DEBUG] Adding MCP server &quot;Git&quot; from built-in.
[DEBUG] Adding MCP server &quot;Memory&quot; from built-in.
[DEBUG] Adding MCP server &quot;Time&quot; from built-in.
[DEBUG] Loading 1 MCP servers from extension &quot;ext1&quot;.
[DEBUG] Adding MCP server &quot;Ext Server 1&quot; from extension &quot;ext1&quot;.
[DEBUG] Loading 1 MCP servers from extension &quot;ext2&quot;.
[DEBUG] Adding MCP server &quot;Ext Server 2&quot; from extension &quot;ext2&quot;.
[DEBUG] Loading 2 MCP servers from user settings.
[DEBUG] Adding MCP server &quot;User Server 1&quot; from user settings.
[DEBUG] Adding MCP server &quot;User Server 2&quot; from user settings.
[DEBUG] Total MCP servers configured: 10

            </system-out>
            <failure message="expected { …(10) } to have property &quot;Calculator&quot;" type="AssertionError">
AssertionError: expected { …(10) } to have property &quot;Calculator&quot;
 ❯ src/config/mcp-integration.test.ts:272:26
            </failure>
        </testcase>
    </testsuite>
    <testsuite name="src/config/settings.test.ts" timestamp="2025-07-02T10:32:34.719Z" hostname="Ajayk" tests="21" failures="0" errors="0" skipped="0" time="0.0800889">
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should load empty settings if no files exist" time="0.0107566">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should load user settings if only user file exists" time="0.0060262">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should load workspace settings if only workspace file exists" time="0.0030421">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should merge user and workspace settings, with workspace taking precedence" time="0.0042702">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should handle contextFileName correctly when only in user settings" time="0.0023559">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should handle contextFileName correctly when only in workspace settings" time="0.0021172">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should default contextFileName to undefined if not in any settings file" time="0.0022822">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should load telemetry setting from user settings" time="0.0021491">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should load telemetry setting from workspace settings" time="0.0022927">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should prioritize workspace telemetry setting over user setting" time="0.0027315">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should have telemetry as undefined if not in any settings file" time="0.0022745">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should handle JSON parsing errors gracefully" time="0.0048944">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should resolve environment variables in user settings" time="0.0027934">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should resolve environment variables in workspace settings" time="0.0025153">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should prioritize workspace env variables over user env variables if keys clash after resolution" time="0.0028498">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should leave unresolved environment variables as is" time="0.0027135">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should resolve multiple environment variables in a single string" time="0.0031048">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should resolve environment variables in arrays" time="0.0026832">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should correctly pass through null, boolean, and number types, and handle undefined properties" time="0.0040664">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should resolve multiple concatenated environment variables in a single string value" time="0.0048811">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; LoadedSettings class &gt; setValue should update the correct scope and recompute merged settings" time="0.0041899">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/App.test.tsx" timestamp="2025-07-02T10:32:34.725Z" hostname="Ajayk" tests="10" failures="6" errors="0" skipped="0" time="1.38203">
        <testcase classname="src/ui/App.test.tsx" name="App UI &gt; should display default &quot;ARIEN.md&quot; in footer when contextFileName is not set and count is 1" time="0.3098457">
            <failure message="expected &apos;\n █████╗ ██████╗ ██╗███████╗███╗   █…&apos; to contain &apos;Using 1 ARIEN.md file&apos;" type="AssertionError">
AssertionError: expected &apos;\n █████╗ ██████╗ ██╗███████╗███╗   █…&apos; to contain &apos;Using 1 ARIEN.md file&apos;

- Expected
+ Received

- Using 1 ARIEN.md file
+
+  █████╗ ██████╗ ██╗███████╗███╗   ██╗     █████╗ ██╗
+ ██╔══██╗██╔══██╗██║██╔════╝████╗  ██║    ██╔══██╗██║
+ ███████║██████╔╝██║█████╗  ██╔██╗ ██║    ███████║██║
+ ██╔══██║██╔══██╗██║██╔══╝  ██║╚██╗██║    ██╔══██║██║
+ ██║  ██║██║  ██║██║███████╗██║ ╚████║    ██║  ██║██║
+ ╚═╝  ╚═╝╚═╝  ╚═╝╚═╝╚══════╝╚═╝  ╚═══╝    ╚═╝  ╚═╝╚═╝
+
+
+  Quick Start Tips
+    • Ask questions, edit files, or run commands
+    • Be specific for the best results
+    • Built-in tools available: file operations, web search, browser automation, and more
+    • Type /help for commands and shortcuts
+
+
+  I&apos;m Feeling Lucky (esc to cancel, 0s)
+
+ Context: 1 ARIEN.md file
+
+ /test/dir                 no sandbox (see /docs)                 model (100% context left)
+

 ❯ src/ui/App.test.tsx:244:25
            </failure>
        </testcase>
        <testcase classname="src/ui/App.test.tsx" name="App UI &gt; should display default &quot;ARIEN.md&quot; with plural when contextFileName is not set and count is &gt; 1" time="0.0848024">
            <failure message="expected &apos;\n █████╗ ██████╗ ██╗███████╗███╗   █…&apos; to contain &apos;Using 2 ARIEN.md files&apos;" type="AssertionError">
AssertionError: expected &apos;\n █████╗ ██████╗ ██╗███████╗███╗   █…&apos; to contain &apos;Using 2 ARIEN.md files&apos;

- Expected
+ Received

- Using 2 ARIEN.md files
+
+  █████╗ ██████╗ ██╗███████╗███╗   ██╗     █████╗ ██╗
+ ██╔══██╗██╔══██╗██║██╔════╝████╗  ██║    ██╔══██╗██║
+ ███████║██████╔╝██║█████╗  ██╔██╗ ██║    ███████║██║
+ ██╔══██║██╔══██╗██║██╔══╝  ██║╚██╗██║    ██╔══██║██║
+ ██║  ██║██║  ██║██║███████╗██║ ╚████║    ██║  ██║██║
+ ╚═╝  ╚═╝╚═╝  ╚═╝╚═╝╚══════╝╚═╝  ╚═══╝    ╚═╝  ╚═╝╚═╝
+
+
+  Quick Start Tips
+    • Ask questions, edit files, or run commands
+    • Be specific for the best results
+    • Built-in tools available: file operations, web search, browser automation, and more
+    • Type /help for commands and shortcuts
+
+
+  I&apos;m Feeling Lucky (esc to cancel, 0s)
+
+ Context: 2 ARIEN.md files
+
+ /test/dir                 no sandbox (see /docs)                 model (100% context left)
+

 ❯ src/ui/App.test.tsx:260:25
            </failure>
        </testcase>
        <testcase classname="src/ui/App.test.tsx" name="App UI &gt; should display custom contextFileName in footer when set and count is 1" time="0.0918732">
            <failure message="expected &apos;\n █████╗ ██████╗ ██╗███████╗███╗   █…&apos; to contain &apos;Using 1 AGENTS.md file&apos;" type="AssertionError">
AssertionError: expected &apos;\n █████╗ ██████╗ ██╗███████╗███╗   █…&apos; to contain &apos;Using 1 AGENTS.md file&apos;

- Expected
+ Received

- Using 1 AGENTS.md file
+
+  █████╗ ██████╗ ██╗███████╗███╗   ██╗     █████╗ ██╗
+ ██╔══██╗██╔══██╗██║██╔════╝████╗  ██║    ██╔══██╗██║
+ ███████║██████╔╝██║█████╗  ██╔██╗ ██║    ███████║██║
+ ██╔══██║██╔══██╗██║██╔══╝  ██║╚██╗██║    ██╔══██║██║
+ ██║  ██║██║  ██║██║███████╗██║ ╚████║    ██║  ██║██║
+ ╚═╝  ╚═╝╚═╝  ╚═╝╚═╝╚══════╝╚═╝  ╚═══╝    ╚═╝  ╚═╝╚═╝
+
+
+  Quick Start Tips
+    • Ask questions, edit files, or run commands
+    • Be specific for the best results
+    • Built-in tools available: file operations, web search, browser automation, and more
+    • Type /help for commands and shortcuts
+
+
+  I&apos;m Feeling Lucky (esc to cancel, 0s)
+
+ Context: 1 AGENTS.md file
+
+ /test/dir                 no sandbox (see /docs)                 model (100% context left)
+

 ❯ src/ui/App.test.tsx:280:25
            </failure>
        </testcase>
        <testcase classname="src/ui/App.test.tsx" name="App UI &gt; should display a generic message when multiple context files with different names are provided" time="0.0992442">
            <failure message="expected &apos;\n █████╗ ██████╗ ██╗███████╗███╗   █…&apos; to contain &apos;Using 2 context files&apos;" type="AssertionError">
AssertionError: expected &apos;\n █████╗ ██████╗ ██╗███████╗███╗   █…&apos; to contain &apos;Using 2 context files&apos;

- Expected
+ Received

- Using 2 context files
+
+  █████╗ ██████╗ ██╗███████╗███╗   ██╗     █████╗ ██╗
+ ██╔══██╗██╔══██╗██║██╔════╝████╗  ██║    ██╔══██╗██║
+ ███████║██████╔╝██║█████╗  ██╔██╗ ██║    ███████║██║
+ ██╔══██║██╔══██╗██║██╔══╝  ██║╚██╗██║    ██╔══██║██║
+ ██║  ██║██║  ██║██║███████╗██║ ╚████║    ██║  ██║██║
+ ╚═╝  ╚═╝╚═╝  ╚═╝╚═╝╚══════╝╚═╝  ╚═══╝    ╚═╝  ╚═╝╚═╝
+
+
+  Quick Start Tips
+    • Ask questions, edit files, or run commands
+    • Be specific for the best results
+    • Built-in tools available: file operations, web search, browser automation, and more
+    • Type /help for commands and shortcuts
+
+
+  I&apos;m Feeling Lucky (esc to cancel, 0s)
+
+ Context: 2 context files
+
+ /test/dir                 no sandbox (see /docs)                 model (100% context left)
+

 ❯ src/ui/App.test.tsx:300:25
            </failure>
        </testcase>
        <testcase classname="src/ui/App.test.tsx" name="App UI &gt; should display custom contextFileName with plural when set and count is &gt; 1" time="0.1060807">
            <failure message="expected &apos;\n █████╗ ██████╗ ██╗███████╗███╗   █…&apos; to contain &apos;Using 3 MY_NOTES.TXT files&apos;" type="AssertionError">
AssertionError: expected &apos;\n █████╗ ██████╗ ██╗███████╗███╗   █…&apos; to contain &apos;Using 3 MY_NOTES.TXT files&apos;

- Expected
+ Received

- Using 3 MY_NOTES.TXT files
+
+  █████╗ ██████╗ ██╗███████╗███╗   ██╗     █████╗ ██╗
+ ██╔══██╗██╔══██╗██║██╔════╝████╗  ██║    ██╔══██╗██║
+ ███████║██████╔╝██║█████╗  ██╔██╗ ██║    ███████║██║
+ ██╔══██║██╔══██╗██║██╔══╝  ██║╚██╗██║    ██╔══██║██║
+ ██║  ██║██║  ██║██║███████╗██║ ╚████║    ██║  ██║██║
+ ╚═╝  ╚═╝╚═╝  ╚═╝╚═╝╚══════╝╚═╝  ╚═══╝    ╚═╝  ╚═╝╚═╝
+
+
+  Quick Start Tips
+    • Ask questions, edit files, or run commands
+    • Be specific for the best results
+    • Built-in tools available: file operations, web search, browser automation, and more
+    • Type /help for commands and shortcuts
+
+
+  I&apos;m Feeling Lucky (esc to cancel, 0s)
+
+ Context: 3 MY_NOTES.TXT files
+
+ /test/dir                 no sandbox (see /docs)                 model (100% context left)
+

 ❯ src/ui/App.test.tsx:320:25
            </failure>
        </testcase>
        <testcase classname="src/ui/App.test.tsx" name="App UI &gt; should not display context file message if count is 0, even if contextFileName is set" time="0.0630031">
        </testcase>
        <testcase classname="src/ui/App.test.tsx" name="App UI &gt; should display ARIEN.md and MCP server count when both are present" time="0.1167286">
        </testcase>
        <testcase classname="src/ui/App.test.tsx" name="App UI &gt; should display only MCP server count when ARIEN.md count is 0" time="0.0857147">
            <failure message="expected &apos;\n █████╗ ██████╗ ██╗███████╗███╗   █…&apos; to contain &apos;Using 2 MCP servers&apos;" type="AssertionError">
AssertionError: expected &apos;\n █████╗ ██████╗ ██╗███████╗███╗   █…&apos; to contain &apos;Using 2 MCP servers&apos;

- Expected
+ Received

- Using 2 MCP servers
+
+  █████╗ ██████╗ ██╗███████╗███╗   ██╗     █████╗ ██╗
+ ██╔══██╗██╔══██╗██║██╔════╝████╗  ██║    ██╔══██╗██║
+ ███████║██████╔╝██║█████╗  ██╔██╗ ██║    ███████║██║
+ ██╔══██║██╔══██╗██║██╔══╝  ██║╚██╗██║    ██╔══██║██║
+ ██║  ██║██║  ██║██║███████╗██║ ╚████║    ██║  ██║██║
+ ╚═╝  ╚═╝╚═╝  ╚═╝╚═╝╚══════╝╚═╝  ╚═══╝    ╚═╝  ╚═╝╚═╝
+
+
+  Quick Start Tips
+    • Ask questions, edit files, or run commands
+    • Be specific for the best results
+    • Built-in tools available: file operations, web search, browser automation, and more
+    • Create ARIEN.md files to customize your interactions
+    • Type /help for commands and shortcuts
+
+
+  I&apos;m Feeling Lucky (esc to cancel, 0s)
+
+ Context: 2 MCP servers (Ctrl+T to view)
+
+ /test/dir                 no sandbox (see /docs)                 model (100% context left)
+

 ❯ src/ui/App.test.tsx:379:25
            </failure>
        </testcase>
        <testcase classname="src/ui/App.test.tsx" name="App UI &gt; when no theme is set &gt; should display theme dialog if NO_COLOR is not set" time="0.3211447">
        </testcase>
        <testcase classname="src/ui/App.test.tsx" name="App UI &gt; when no theme is set &gt; should display a message if NO_COLOR is set" time="0.0991146">
        </testcase>
    </testsuite>
    <testsuite name="src/utils/startupWarnings.test.ts" timestamp="2025-07-02T10:32:34.734Z" hostname="Ajayk" tests="4" failures="0" errors="0" skipped="4" time="0">
        <testcase classname="src/utils/startupWarnings.test.ts" name="startupWarnings &gt; should return warnings from the file and delete it" time="0">
            <skipped/>
        </testcase>
        <testcase classname="src/utils/startupWarnings.test.ts" name="startupWarnings &gt; should return an empty array if the file does not exist" time="0">
            <skipped/>
        </testcase>
        <testcase classname="src/utils/startupWarnings.test.ts" name="startupWarnings &gt; should return an error message if reading the file fails" time="0">
            <skipped/>
        </testcase>
        <testcase classname="src/utils/startupWarnings.test.ts" name="startupWarnings &gt; should return a warning if deleting the file fails" time="0">
            <skipped/>
        </testcase>
    </testsuite>
    <testsuite name="src/ui/components/AuthDialog.test.tsx" timestamp="2025-07-02T10:32:34.736Z" hostname="Ajayk" tests="3" failures="0" errors="0" skipped="0" time="0.4105428">
        <testcase classname="src/ui/components/AuthDialog.test.tsx" name="AuthDialog &gt; should show an error if the initial auth type is invalid" time="0.1019823">
        </testcase>
        <testcase classname="src/ui/components/AuthDialog.test.tsx" name="AuthDialog &gt; should prevent exiting when no auth method is selected and show error message" time="0.1514963">
        </testcase>
        <testcase classname="src/ui/components/AuthDialog.test.tsx" name="AuthDialog &gt; should allow exiting when auth method is already selected" time="0.1525438">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/components/HistoryItemDisplay.test.tsx" timestamp="2025-07-02T10:32:34.737Z" hostname="Ajayk" tests="4" failures="1" errors="0" skipped="0" time="0.3527847">
        <testcase classname="src/ui/components/HistoryItemDisplay.test.tsx" name="&lt;HistoryItemDisplay /&gt; &gt; renders UserMessage for &quot;user&quot; type" time="0.0752895">
        </testcase>
        <testcase classname="src/ui/components/HistoryItemDisplay.test.tsx" name="&lt;HistoryItemDisplay /&gt; &gt; renders StatsDisplay for &quot;stats&quot; type" time="0.0632014">
        </testcase>
        <testcase classname="src/ui/components/HistoryItemDisplay.test.tsx" name="&lt;HistoryItemDisplay /&gt; &gt; renders AboutBox for &quot;about&quot; type" time="0.0556168">
        </testcase>
        <testcase classname="src/ui/components/HistoryItemDisplay.test.tsx" name="&lt;HistoryItemDisplay /&gt; &gt; renders SessionSummaryDisplay for &quot;quit&quot; type" time="0.153556">
            <failure message="expected &apos;╭────────────────────────────────────…&apos; to contain &apos;Agent powering down. Goodbye!&apos;" type="AssertionError">
AssertionError: expected &apos;╭────────────────────────────────────…&apos; to contain &apos;Agent powering down. Goodbye!&apos;

- Expected
+ Received

- Agent powering down. Goodbye!
+ ╭────────────────────────────────────────────╮
+ │                                            │
+ │              Session Complete              │
+ │        Thank you for using Arien AI        │
+ │                                            │
+ │  ────────────────────────────────────────  │
+ │                                            │
+ │                                            │
+ │  Cumulative Stats (1 Turns)                │
+ │                                            │
+ │  Input Tokens             10               │
+ │  Output Tokens            20               │
+ │  Tool Use Tokens           2               │
+ │  Thoughts Tokens           3               │
+ │  Cached Tokens     5 (16.7%)               │
+ │  ───────────────────────────               │
+ │  Total Tokens             30               │
+ │                                            │
+ │  Total duration (API)  123ms               │
+ │  Total duration (wall)    1s               │
+ │                                            │
+ ╰────────────────────────────────────────────╯

 ❯ src/ui/components/HistoryItemDisplay.test.tsx:99:25
            </failure>
        </testcase>
    </testsuite>
    <testsuite name="src/ui/components/InputPrompt.test.tsx" timestamp="2025-07-02T10:32:34.739Z" hostname="Ajayk" tests="5" failures="0" errors="0" skipped="0" time="0.975061">
        <testcase classname="src/ui/components/InputPrompt.test.tsx" name="InputPrompt &gt; should call shellHistory.getPreviousCommand on up arrow in shell mode" time="0.2704826">
        </testcase>
        <testcase classname="src/ui/components/InputPrompt.test.tsx" name="InputPrompt &gt; should call shellHistory.getNextCommand on down arrow in shell mode" time="0.161908">
        </testcase>
        <testcase classname="src/ui/components/InputPrompt.test.tsx" name="InputPrompt &gt; should set the buffer text when a shell history command is retrieved" time="0.145071">
        </testcase>
        <testcase classname="src/ui/components/InputPrompt.test.tsx" name="InputPrompt &gt; should call shellHistory.addCommandToHistory on submit in shell mode" time="0.1302295">
        </testcase>
        <testcase classname="src/ui/components/InputPrompt.test.tsx" name="InputPrompt &gt; should NOT call shell history methods when not in shell mode" time="0.2596229">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/components/LoadingIndicator.test.tsx" timestamp="2025-07-02T10:32:34.741Z" hostname="Ajayk" tests="10" failures="0" errors="0" skipped="0" time="0.2169642">
        <testcase classname="src/ui/components/LoadingIndicator.test.tsx" name="&lt;LoadingIndicator /&gt; &gt; should not render when streamingState is Idle" time="0.0468739">
        </testcase>
        <testcase classname="src/ui/components/LoadingIndicator.test.tsx" name="&lt;LoadingIndicator /&gt; &gt; should render spinner, phrase, and time when streamingState is Responding" time="0.0489314">
        </testcase>
        <testcase classname="src/ui/components/LoadingIndicator.test.tsx" name="&lt;LoadingIndicator /&gt; &gt; should render spinner (static), phrase but no time/cancel when streamingState is WaitingForConfirmation" time="0.0083519">
        </testcase>
        <testcase classname="src/ui/components/LoadingIndicator.test.tsx" name="&lt;LoadingIndicator /&gt; &gt; should display the currentLoadingPhrase correctly" time="0.0072838">
        </testcase>
        <testcase classname="src/ui/components/LoadingIndicator.test.tsx" name="&lt;LoadingIndicator /&gt; &gt; should display the elapsedTime correctly when Responding" time="0.019914">
        </testcase>
        <testcase classname="src/ui/components/LoadingIndicator.test.tsx" name="&lt;LoadingIndicator /&gt; &gt; should render rightContent when provided" time="0.0254044">
        </testcase>
        <testcase classname="src/ui/components/LoadingIndicator.test.tsx" name="&lt;LoadingIndicator /&gt; &gt; should transition correctly between states using rerender" time="0.0231054">
        </testcase>
        <testcase classname="src/ui/components/LoadingIndicator.test.tsx" name="&lt;LoadingIndicator /&gt; &gt; should display fallback phrase if thought is empty" time="0.0068883">
        </testcase>
        <testcase classname="src/ui/components/LoadingIndicator.test.tsx" name="&lt;LoadingIndicator /&gt; &gt; should display the subject of a thought" time="0.0078917">
        </testcase>
        <testcase classname="src/ui/components/LoadingIndicator.test.tsx" name="&lt;LoadingIndicator /&gt; &gt; should prioritize thought.subject over currentLoadingPhrase" time="0.0157828">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/components/SessionSummaryDisplay.test.tsx" timestamp="2025-07-02T10:32:34.744Z" hostname="Ajayk" tests="2" failures="0" errors="0" skipped="0" time="0.1622374">
        <testcase classname="src/ui/components/SessionSummaryDisplay.test.tsx" name="&lt;SessionSummaryDisplay /&gt; &gt; renders correctly with given stats and duration" time="0.1316318">
        </testcase>
        <testcase classname="src/ui/components/SessionSummaryDisplay.test.tsx" name="&lt;SessionSummaryDisplay /&gt; &gt; renders zero state correctly" time="0.0257153">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/components/Stats.test.tsx" timestamp="2025-07-02T10:32:34.745Z" hostname="Ajayk" tests="7" failures="0" errors="0" skipped="0" time="0.2327901">
        <testcase classname="src/ui/components/Stats.test.tsx" name="&lt;StatRow /&gt; &gt; renders a label and value" time="0.0695836">
        </testcase>
        <testcase classname="src/ui/components/Stats.test.tsx" name="&lt;StatRow /&gt; &gt; renders with a specific value color" time="0.0201448">
        </testcase>
        <testcase classname="src/ui/components/Stats.test.tsx" name="&lt;StatsColumn /&gt; &gt; renders a stats column with children" time="0.0504999">
        </testcase>
        <testcase classname="src/ui/components/Stats.test.tsx" name="&lt;StatsColumn /&gt; &gt; renders a stats column with a specific width" time="0.0251025">
        </testcase>
        <testcase classname="src/ui/components/Stats.test.tsx" name="&lt;StatsColumn /&gt; &gt; renders a cumulative stats column with percentages" time="0.0271425">
        </testcase>
        <testcase classname="src/ui/components/Stats.test.tsx" name="&lt;StatsColumn /&gt; &gt; hides the tool use row when there are no tool use tokens" time="0.0192462">
        </testcase>
        <testcase classname="src/ui/components/Stats.test.tsx" name="&lt;DurationColumn /&gt; &gt; renders a duration column" time="0.0130577">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/components/StatsDisplay.test.tsx" timestamp="2025-07-02T10:32:34.747Z" hostname="Ajayk" tests="2" failures="0" errors="0" skipped="0" time="0.1582512">
        <testcase classname="src/ui/components/StatsDisplay.test.tsx" name="&lt;StatsDisplay /&gt; &gt; renders correctly with given stats and duration" time="0.1237302">
        </testcase>
        <testcase classname="src/ui/components/StatsDisplay.test.tsx" name="&lt;StatsDisplay /&gt; &gt; renders zero state correctly" time="0.0297017">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/contexts/SessionContext.test.tsx" timestamp="2025-07-02T10:32:34.748Z" hostname="Ajayk" tests="6" failures="0" errors="0" skipped="0" time="0.1443708">
        <testcase classname="src/ui/contexts/SessionContext.test.tsx" name="SessionStatsContext &gt; should provide the correct initial state" time="0.0481723">
            <system-err>
An update to Root inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() =&gt; {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you&apos;re testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act

            </system-err>
        </testcase>
        <testcase classname="src/ui/contexts/SessionContext.test.tsx" name="SessionStatsContext &gt; should increment turnCount when startNewTurn is called" time="0.0206115">
            <system-err>
An update to Root inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() =&gt; {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you&apos;re testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act
`ReactDOMTestUtils.act` is deprecated in favor of `React.act`. Import `act` from `react` instead of `react-dom/test-utils`. See https://react.dev/warnings/react-dom-test-utils for more info.

            </system-err>
        </testcase>
        <testcase classname="src/ui/contexts/SessionContext.test.tsx" name="SessionStatsContext &gt; should aggregate token usage correctly when addUsage is called" time="0.0054299">
            <system-err>
An update to Root inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() =&gt; {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you&apos;re testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act

            </system-err>
        </testcase>
        <testcase classname="src/ui/contexts/SessionContext.test.tsx" name="SessionStatsContext &gt; should correctly track a full logical turn with multiple API calls" time="0.0055439">
            <system-err>
An update to Root inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() =&gt; {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you&apos;re testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act

            </system-err>
        </testcase>
        <testcase classname="src/ui/contexts/SessionContext.test.tsx" name="SessionStatsContext &gt; should overwrite currentResponse with each API call" time="0.0043627">
            <system-err>
An update to Root inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() =&gt; {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you&apos;re testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act

            </system-err>
        </testcase>
        <testcase classname="src/ui/contexts/SessionContext.test.tsx" name="SessionStatsContext &gt; should throw an error when useSessionStats is used outside of a provider" time="0.054281">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/utils/errorParsing.test.ts" timestamp="2025-07-02T10:32:34.751Z" hostname="Ajayk" tests="11" failures="0" errors="0" skipped="0" time="0.0151204">
        <testcase classname="src/ui/utils/errorParsing.test.ts" name="parseAndFormatApiError &gt; should format a valid API error JSON" time="0.004807">
        </testcase>
        <testcase classname="src/ui/utils/errorParsing.test.ts" name="parseAndFormatApiError &gt; should format a 429 API error with the default message" time="0.0012919">
        </testcase>
        <testcase classname="src/ui/utils/errorParsing.test.ts" name="parseAndFormatApiError &gt; should format a 429 API error with the personal message" time="0.000618">
        </testcase>
        <testcase classname="src/ui/utils/errorParsing.test.ts" name="parseAndFormatApiError &gt; should format a 429 API error with the vertex message" time="0.0006172">
        </testcase>
        <testcase classname="src/ui/utils/errorParsing.test.ts" name="parseAndFormatApiError &gt; should return the original message if it is not a JSON error" time="0.0004452">
        </testcase>
        <testcase classname="src/ui/utils/errorParsing.test.ts" name="parseAndFormatApiError &gt; should return the original message for malformed JSON" time="0.0004973">
        </testcase>
        <testcase classname="src/ui/utils/errorParsing.test.ts" name="parseAndFormatApiError &gt; should handle JSON that does not match the ApiError structure" time="0.0005219">
        </testcase>
        <testcase classname="src/ui/utils/errorParsing.test.ts" name="parseAndFormatApiError &gt; should format a nested API error" time="0.0008547">
        </testcase>
        <testcase classname="src/ui/utils/errorParsing.test.ts" name="parseAndFormatApiError &gt; should format a StructuredError" time="0.0005291">
        </testcase>
        <testcase classname="src/ui/utils/errorParsing.test.ts" name="parseAndFormatApiError &gt; should format a 429 StructuredError with the vertex message" time="0.0005118">
        </testcase>
        <testcase classname="src/ui/utils/errorParsing.test.ts" name="parseAndFormatApiError &gt; should handle an unknown error type" time="0.0003292">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/utils/formatters.test.ts" timestamp="2025-07-02T10:32:34.753Z" hostname="Ajayk" tests="14" failures="0" errors="0" skipped="0" time="0.018116">
        <testcase classname="src/ui/utils/formatters.test.ts" name="formatters &gt; formatMemoryUsage &gt; should format bytes into KB" time="0.0064819">
        </testcase>
        <testcase classname="src/ui/utils/formatters.test.ts" name="formatters &gt; formatMemoryUsage &gt; should format bytes into MB" time="0.0005441">
        </testcase>
        <testcase classname="src/ui/utils/formatters.test.ts" name="formatters &gt; formatMemoryUsage &gt; should format bytes into GB" time="0.0005435">
        </testcase>
        <testcase classname="src/ui/utils/formatters.test.ts" name="formatters &gt; formatDuration &gt; should format milliseconds less than a second" time="0.0008639">
        </testcase>
        <testcase classname="src/ui/utils/formatters.test.ts" name="formatters &gt; formatDuration &gt; should format a duration of 0" time="0.000388">
        </testcase>
        <testcase classname="src/ui/utils/formatters.test.ts" name="formatters &gt; formatDuration &gt; should format an exact number of seconds" time="0.0003487">
        </testcase>
        <testcase classname="src/ui/utils/formatters.test.ts" name="formatters &gt; formatDuration &gt; should format a duration in seconds with one decimal place" time="0.0003298">
        </testcase>
        <testcase classname="src/ui/utils/formatters.test.ts" name="formatters &gt; formatDuration &gt; should format an exact number of minutes" time="0.0003868">
        </testcase>
        <testcase classname="src/ui/utils/formatters.test.ts" name="formatters &gt; formatDuration &gt; should format a duration in minutes and seconds" time="0.0004237">
        </testcase>
        <testcase classname="src/ui/utils/formatters.test.ts" name="formatters &gt; formatDuration &gt; should format an exact number of hours" time="0.0003559">
        </testcase>
        <testcase classname="src/ui/utils/formatters.test.ts" name="formatters &gt; formatDuration &gt; should format a duration in hours and seconds" time="0.000436">
        </testcase>
        <testcase classname="src/ui/utils/formatters.test.ts" name="formatters &gt; formatDuration &gt; should format a duration in hours, minutes, and seconds" time="0.0006866">
        </testcase>
        <testcase classname="src/ui/utils/formatters.test.ts" name="formatters &gt; formatDuration &gt; should handle large durations" time="0.0005303">
        </testcase>
        <testcase classname="src/ui/utils/formatters.test.ts" name="formatters &gt; formatDuration &gt; should handle negative durations" time="0.0003641">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/utils/markdownUtilities.test.ts" timestamp="2025-07-02T10:32:34.757Z" hostname="Ajayk" tests="7" failures="0" errors="0" skipped="0" time="0.0107823">
        <testcase classname="src/ui/utils/markdownUtilities.test.ts" name="markdownUtilities &gt; findLastSafeSplitPoint &gt; should split at the last double newline if not in a code block" time="0.0042244">
        </testcase>
        <testcase classname="src/ui/utils/markdownUtilities.test.ts" name="markdownUtilities &gt; findLastSafeSplitPoint &gt; should return content.length if no safe split point is found" time="0.0005398">
        </testcase>
        <testcase classname="src/ui/utils/markdownUtilities.test.ts" name="markdownUtilities &gt; findLastSafeSplitPoint &gt; should prioritize splitting at 

 over being at the very end of the string if the end is not in a code block" time="0.0004747">
        </testcase>
        <testcase classname="src/ui/utils/markdownUtilities.test.ts" name="markdownUtilities &gt; findLastSafeSplitPoint &gt; should return content.length if the only 

 is inside a code block and the end of content is not" time="0.0004782">
        </testcase>
        <testcase classname="src/ui/utils/markdownUtilities.test.ts" name="markdownUtilities &gt; findLastSafeSplitPoint &gt; should correctly identify the last 

 even if it is followed by text not in a code block" time="0.0004332">
        </testcase>
        <testcase classname="src/ui/utils/markdownUtilities.test.ts" name="markdownUtilities &gt; findLastSafeSplitPoint &gt; should return content.length if content is empty" time="0.0003392">
        </testcase>
        <testcase classname="src/ui/utils/markdownUtilities.test.ts" name="markdownUtilities &gt; findLastSafeSplitPoint &gt; should return content.length if content has no newlines and no code blocks" time="0.0003314">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/utils/textUtils.test.ts" timestamp="2025-07-02T10:32:34.758Z" hostname="Ajayk" tests="5" failures="0" errors="0" skipped="0" time="0.0110705">
        <testcase classname="src/ui/utils/textUtils.test.ts" name="textUtils &gt; isBinary &gt; should return true for a buffer containing a null byte" time="0.0043779">
        </testcase>
        <testcase classname="src/ui/utils/textUtils.test.ts" name="textUtils &gt; isBinary &gt; should return false for a buffer containing only text" time="0.0006172">
        </testcase>
        <testcase classname="src/ui/utils/textUtils.test.ts" name="textUtils &gt; isBinary &gt; should return false for an empty buffer" time="0.0004499">
        </testcase>
        <testcase classname="src/ui/utils/textUtils.test.ts" name="textUtils &gt; isBinary &gt; should return false for a null or undefined buffer" time="0.000487">
        </testcase>
        <testcase classname="src/ui/utils/textUtils.test.ts" name="textUtils &gt; isBinary &gt; should only check the sample size" time="0.0006105">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/atCommandProcessor.test.ts" timestamp="2025-07-02T10:32:34.760Z" hostname="Ajayk" tests="17" failures="1" errors="0" skipped="0" time="0.1234333">
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; should pass through query if no @ command is present" time="0.0156579">
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; should pass through original query if only a lone @ symbol is present" time="0.0030826">
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; should process a valid text file path" time="0.0107763">
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; should process a valid directory path and convert to glob" time="0.0042077">
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; should process a valid image file path (as text content for now)" time="0.0018997">
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; should handle query with text before and after @command" time="0.0026893">
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; should correctly unescape paths with escaped spaces" time="0.0028035">
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; should handle multiple @file references" time="0.0033526">
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; should handle multiple @file references with interleaved text" time="0.0036431">
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; should handle a mix of valid, invalid, and lone @ references" time="0.0351667">
            <failure message="expected &quot;spy&quot; to be called with arguments: [ { paths: [ …(2) ], …(1) }, …(1) ][90m

Received: 

[1m  1st spy call:

[22m[2m  [[22m
[2m    {[22m
[2m      &quot;paths&quot;: [[22m
[2m        &quot;valid1.txt&quot;,[22m
[32m-       &quot;resolved/valid2.actual&quot;,[90m
[31m+       &quot;resolved\\valid2.actual&quot;,[90m
[2m      ],[22m
[2m      &quot;respectGitIgnore&quot;: true,[22m
[2m    },[22m
[2m    AbortSignal {},[22m
[2m  ][22m
[39m[90m

Number of calls: [1m1[22m
[39m" type="AssertionError">
AssertionError: expected &quot;spy&quot; to be called with arguments: [ { paths: [ …(2) ], …(1) }, …(1) ]

Received: 

  1st spy call:

  [
    {
      &quot;paths&quot;: [
        &quot;valid1.txt&quot;,
-       &quot;resolved/valid2.actual&quot;,
+       &quot;resolved\\valid2.actual&quot;,
      ],
      &quot;respectGitIgnore&quot;: true,
    },
    AbortSignal {},
  ]


Number of calls: 1

 ❯ src/ui/hooks/atCommandProcessor.test.ts:455:38
            </failure>
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; should return original query if all @paths are invalid or lone @" time="0.0036519">
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; should process a file path case-insensitively" time="0.0055545">
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; git-aware filtering &gt; should skip git-ignored files in @ commands" time="0.0030974">
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; git-aware filtering &gt; should process non-git-ignored files normally" time="0.0082605">
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; git-aware filtering &gt; should handle mixed git-ignored and valid files" time="0.0056135">
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; git-aware filtering &gt; should always ignore .git directory files" time="0.0032786">
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; when recursive file search is disabled &gt; should not use glob search for a nonexistent file" time="0.0038924">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/shellCommandProcessor.test.ts" timestamp="2025-07-02T10:32:34.765Z" hostname="Ajayk" tests="3" failures="0" errors="0" skipped="0" time="0.1119854">
        <testcase classname="src/ui/hooks/shellCommandProcessor.test.ts" name="useShellCommandProcessor &gt; should execute a command and update history on success" time="0.0802932">
        </testcase>
        <testcase classname="src/ui/hooks/shellCommandProcessor.test.ts" name="useShellCommandProcessor &gt; should handle binary output" time="0.0118246">
        </testcase>
        <testcase classname="src/ui/hooks/shellCommandProcessor.test.ts" name="useShellCommandProcessor &gt; should handle command failure" time="0.0143959">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/slashCommandProcessor.test.ts" timestamp="2025-07-02T10:32:34.766Z" hostname="Ajayk" tests="30" failures="2" errors="0" skipped="0" time="0.5799568">
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /memory add &gt; should return tool scheduling info on valid input" time="0.064062">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /memory add &gt; should show usage error and return true if no text is provided" time="0.006485">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /memory show &gt; should call the showMemoryAction and return true" time="0.0210083">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /memory refresh &gt; should call performMemoryRefresh and return true" time="0.0169104">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; Unknown /memory subcommand &gt; should show an error for unknown /memory subcommand and return true" time="0.0128795">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /stats command &gt; should show detailed session statistics" time="0.021495">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /about command &gt; should show the about box with all details including auth and project" time="0.0075719">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /about command &gt; should show sandbox-exec profile when applicable" time="0.0061672">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; Other commands &gt; /help should open help and return true" time="0.0099859">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; Other commands &gt; /clear should clear items, reset chat, and refresh static" time="0.005236">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; Other commands &gt; /editor should open editor dialog and return true" time="0.0054583">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /bug command &gt; should call open with the correct GitHub issue URL and return true" time="0.0093472">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /bug command &gt; should use the custom bug command URL from config if available" time="0.0083121">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /quit and /exit commands &gt; should handle /quit, set quitting messages, and exit the process" time="0.0157448">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /quit and /exit commands &gt; should handle /exit, set quitting messages, and exit the process" time="0.0079673">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; Unknown command &gt; should show an error and return true for a general unknown command" time="0.0053519">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /tools command &gt; should show an error if tool registry is not available" time="0.0063742">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /tools command &gt; should show an error if getAllTools returns undefined" time="0.0064274">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /tools command &gt; should display only Arien CLI tools (filtering out MCP tools)" time="0.0496653">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /tools command &gt; should display a message when no Arien CLI tools are available" time="0.0054446">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /tools command &gt; should display tool descriptions when /tools desc is used" time="0.0063479">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /mcp command &gt; should show an error if tool registry is not available" time="0.0080482">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /mcp command &gt; should display a message with a URL when no MCP servers are configured in a sandbox" time="0.0476447">
            <failure message="expected 2nd &quot;spy&quot; call to have been called with [ ObjectContaining{…}, Any&lt;Number&gt; ]" type="AssertionError">
AssertionError: expected 2nd &quot;spy&quot; call to have been called with [ ObjectContaining{…}, Any&lt;Number&gt; ]

- Expected
+ Received

  [
-   ObjectContaining {
-     &quot;text&quot;: &quot;No MCP servers configured. Please open the following URL in your browser to view documentation:
+   {
+     &quot;text&quot;: &quot;No MCP servers found. This is unusual as Arien AI includes 12 built-in MCP servers by default. Built-in servers may be disabled in your settings. Please open the following URL in your browser to view documentation:
  https://goo.gle/gemini-cli-docs-mcp&quot;,
      &quot;type&quot;: &quot;info&quot;,
    },
    Any&lt;Number&gt;,
  ]

 ❯ src/ui/hooks/slashCommandProcessor.test.ts:835:27
            </failure>
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /mcp command &gt; should display a message and open a URL when no MCP servers are configured outside a sandbox" time="0.0356103">
            <failure message="expected 2nd &quot;spy&quot; call to have been called with [ ObjectContaining{…}, Any&lt;Number&gt; ]" type="AssertionError">
AssertionError: expected 2nd &quot;spy&quot; call to have been called with [ ObjectContaining{…}, Any&lt;Number&gt; ]

- Expected
+ Received

  [
-   ObjectContaining {
-     &quot;text&quot;: &quot;No MCP servers configured. Opening documentation in your browser: https://goo.gle/gemini-cli-docs-mcp&quot;,
+   {
+     &quot;text&quot;: &quot;No MCP servers found. This is unusual as Arien AI includes 12 built-in MCP servers by default. Built-in servers may be disabled in your settings. Please open the following URL in your browser to view documentation:
+ https://goo.gle/gemini-cli-docs-mcp&quot;,
      &quot;type&quot;: &quot;info&quot;,
    },
    Any&lt;Number&gt;,
  ]

 ❯ src/ui/hooks/slashCommandProcessor.test.ts:862:27
            </failure>
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /mcp command &gt; should display configured MCP servers with status indicators and their tools" time="0.0766146">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /mcp command &gt; should display tool descriptions when showToolDescriptions is true" time="0.0073506">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /mcp command &gt; should indicate when a server has no tools" time="0.0077388">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /mcp command &gt; should show startup indicator when servers are connecting" time="0.0409904">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /mcp schema &gt; should display tool schemas and descriptions" time="0.0156385">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /compress command &gt; should call tryCompressChat(true)" time="0.0348161">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/useArienStream.test.tsx" timestamp="2025-07-02T10:32:34.775Z" hostname="Ajayk" tests="24" failures="0" errors="0" skipped="0" time="0.633318">
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="mergePartListUnions &gt; should merge multiple PartListUnion arrays" time="0.0085568">
        </testcase>
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="mergePartListUnions &gt; should handle empty arrays in the input list" time="0.0008081">
        </testcase>
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="mergePartListUnions &gt; should handle a single PartListUnion array" time="0.0004949">
        </testcase>
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="mergePartListUnions &gt; should return an empty array if all input arrays are empty" time="0.0005411">
        </testcase>
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="mergePartListUnions &gt; should handle input list being empty" time="0.0003881">
        </testcase>
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="mergePartListUnions &gt; should correctly merge when PartListUnion items are single Parts not in arrays" time="0.0005697">
        </testcase>
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="mergePartListUnions &gt; should handle a mix of arrays and single parts, including empty arrays and undefined/null parts if they were possible (though PartListUnion typing restricts this)" time="0.0005632">
        </testcase>
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="mergePartListUnions &gt; should preserve the order of parts from the input arrays" time="0.0006288">
        </testcase>
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="mergePartListUnions &gt; should handle cases where some PartListUnion items are single Parts and others are arrays of Parts" time="0.0007811">
        </testcase>
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="useArienStream &gt; should not submit tool responses if not all tool calls are completed" time="0.062555">
        </testcase>
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="useArienStream &gt; should submit tool responses when all tool calls are completed and ready" time="0.0356684">
        </testcase>
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="useArienStream &gt; should handle all tool calls being cancelled" time="0.0289607">
        </testcase>
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="useArienStream &gt; Session Stats Integration &gt; should call startNewTurn and addUsage for a simple prompt" time="0.154896">
        </testcase>
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="useArienStream &gt; Session Stats Integration &gt; should only call addUsage for a tool continuation prompt" time="0.0099215">
        </testcase>
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="useArienStream &gt; Session Stats Integration &gt; should not call addUsage if the stream contains no usage metadata" time="0.0158575">
        </testcase>
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="useArienStream &gt; Session Stats Integration &gt; should not call startNewTurn for a slash command" time="0.0477437">
        </testcase>
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="useArienStream &gt; should not flicker streaming state to Idle between tool completion and submission" time="0.0381832">
        </testcase>
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="useArienStream &gt; User Cancellation &gt; should cancel an in-progress stream when escape is pressed" time="0.0593586">
        </testcase>
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="useArienStream &gt; User Cancellation &gt; should not do anything if escape is pressed when not responding" time="0.0088727">
        </testcase>
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="useArienStream &gt; User Cancellation &gt; should prevent further processing after cancellation" time="0.0788479">
            <system-err>
An update to TestComponent inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() =&gt; {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you&apos;re testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act

            </system-err>
        </testcase>
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="useArienStream &gt; User Cancellation &gt; should not cancel if a tool call is in progress (not just responding)" time="0.0098575">
        </testcase>
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="useArienStream &gt; Client-Initiated Tool Calls &gt; should execute a client-initiated tool without sending a response to AI" time="0.0145681">
        </testcase>
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="useArienStream &gt; Memory Refresh on save_memory &gt; should call performMemoryRefresh when a save_memory tool call completes successfully" time="0.0224211">
        </testcase>
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="useArienStream &gt; Error Handling &gt; should call parseAndFormatApiError with the correct authType on stream initialization failure" time="0.0255636">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/useAutoAcceptIndicator.test.ts" timestamp="2025-07-02T10:32:34.781Z" hostname="Ajayk" tests="6" failures="0" errors="0" skipped="0" time="0.1020362">
        <testcase classname="src/ui/hooks/useAutoAcceptIndicator.test.ts" name="useAutoAcceptIndicator &gt; should initialize with ApprovalMode.AUTO_EDIT if config.getApprovalMode returns ApprovalMode.AUTO_EDIT" time="0.0450479">
        </testcase>
        <testcase classname="src/ui/hooks/useAutoAcceptIndicator.test.ts" name="useAutoAcceptIndicator &gt; should initialize with ApprovalMode.DEFAULT if config.getApprovalMode returns ApprovalMode.DEFAULT" time="0.0073077">
        </testcase>
        <testcase classname="src/ui/hooks/useAutoAcceptIndicator.test.ts" name="useAutoAcceptIndicator &gt; should initialize with ApprovalMode.YOLO if config.getApprovalMode returns ApprovalMode.YOLO" time="0.0066649">
        </testcase>
        <testcase classname="src/ui/hooks/useAutoAcceptIndicator.test.ts" name="useAutoAcceptIndicator &gt; should toggle the indicator and update config when Shift+Tab or Ctrl+Y is pressed" time="0.0212822">
        </testcase>
        <testcase classname="src/ui/hooks/useAutoAcceptIndicator.test.ts" name="useAutoAcceptIndicator &gt; should not toggle if only one key or other keys combinations are pressed" time="0.0090315">
        </testcase>
        <testcase classname="src/ui/hooks/useAutoAcceptIndicator.test.ts" name="useAutoAcceptIndicator &gt; should update indicator when config value changes externally (useEffect dependency)" time="0.0071479">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/useCompletion.integration.test.ts" timestamp="2025-07-02T10:32:34.783Z" hostname="Ajayk" tests="9" failures="3" errors="0" skipped="0" time="1.5860082">
        <testcase classname="src/ui/hooks/useCompletion.integration.test.ts" name="useCompletion git-aware filtering integration &gt; should filter git-ignored entries from @ completions" time="0.2223506">
        </testcase>
        <testcase classname="src/ui/hooks/useCompletion.integration.test.ts" name="useCompletion git-aware filtering integration &gt; should filter git-ignored directories from @ completions" time="0.1612292">
        </testcase>
        <testcase classname="src/ui/hooks/useCompletion.integration.test.ts" name="useCompletion git-aware filtering integration &gt; should handle recursive search with git-aware filtering" time="0.1590762">
            <system-err>
Error fetching completion suggestions for t: Cannot read properties of undefined (reading &apos;map&apos;)

            </system-err>
        </testcase>
        <testcase classname="src/ui/hooks/useCompletion.integration.test.ts" name="useCompletion git-aware filtering integration &gt; should not perform recursive search when disabled in config" time="0.1972844">
            <failure message="expected &quot;readdir&quot; to be called with arguments: [ &apos;/test/project&apos;, …(1) ][90m

Received: 

[1m  1st readdir call:

[22m[2m  [[22m
[32m-   &quot;/test/project&quot;,[90m
[31m+   &quot;C:\\test\\project&quot;,[90m
[2m    {[22m
[2m      &quot;withFileTypes&quot;: true,[22m
[2m    },[22m
[2m  ][22m
[39m[90m

Number of calls: [1m1[22m
[39m" type="AssertionError">
AssertionError: expected &quot;readdir&quot; to be called with arguments: [ &apos;/test/project&apos;, …(1) ]

Received: 

  1st readdir call:

  [
-   &quot;/test/project&quot;,
+   &quot;C:\\test\\project&quot;,
    {
      &quot;withFileTypes&quot;: true,
    },
  ]


Number of calls: 1

 ❯ src/ui/hooks/useCompletion.integration.test.ts:212:24
            </failure>
        </testcase>
        <testcase classname="src/ui/hooks/useCompletion.integration.test.ts" name="useCompletion git-aware filtering integration &gt; should work without config (fallback behavior)" time="0.1687405">
        </testcase>
        <testcase classname="src/ui/hooks/useCompletion.integration.test.ts" name="useCompletion git-aware filtering integration &gt; should handle git discovery service initialization failure gracefully" time="0.1656674">
        </testcase>
        <testcase classname="src/ui/hooks/useCompletion.integration.test.ts" name="useCompletion git-aware filtering integration &gt; should handle directory-specific completions with git filtering" time="0.1676045">
        </testcase>
        <testcase classname="src/ui/hooks/useCompletion.integration.test.ts" name="useCompletion git-aware filtering integration &gt; should use glob for top-level @ completions when available" time="0.1737722">
            <failure message="expected [ { label: &apos;README.md&apos;, …(1) }, …(1) ] to deeply equal [ { label: &apos;README.md&apos;, …(1) }, …(1) ]" type="AssertionError">
AssertionError: expected [ { label: &apos;README.md&apos;, …(1) }, …(1) ] to deeply equal [ { label: &apos;README.md&apos;, …(1) }, …(1) ]

- Expected
+ Received

@@ -2,9 +2,9 @@
    {
      &quot;label&quot;: &quot;README.md&quot;,
      &quot;value&quot;: &quot;README.md&quot;,
    },
    {
-     &quot;label&quot;: &quot;src/index.ts&quot;,
-     &quot;value&quot;: &quot;src/index.ts&quot;,
+     &quot;label&quot;: &quot;src\\index.ts&quot;,
+     &quot;value&quot;: &quot;src\\index.ts&quot;,
    },
  ]

 ❯ src/ui/hooks/useCompletion.integration.test.ts:309:40
            </failure>
        </testcase>
        <testcase classname="src/ui/hooks/useCompletion.integration.test.ts" name="useCompletion git-aware filtering integration &gt; should include dotfiles in glob search when input starts with a dot" time="0.1652905">
            <failure message="expected [ …(3) ] to deeply equal [ …(3) ]" type="AssertionError">
AssertionError: expected [ …(3) ] to deeply equal [ …(3) ]

- Expected
+ Received

@@ -6,9 +6,9 @@
    {
      &quot;label&quot;: &quot;.gitignore&quot;,
      &quot;value&quot;: &quot;.gitignore&quot;,
    },
    {
-     &quot;label&quot;: &quot;src/index.ts&quot;,
-     &quot;value&quot;: &quot;src/index.ts&quot;,
+     &quot;label&quot;: &quot;src\\index.ts&quot;,
+     &quot;value&quot;: &quot;src\\index.ts&quot;,
    },
  ]

 ❯ src/ui/hooks/useCompletion.integration.test.ts:337:40
            </failure>
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/useConsoleMessages.test.ts" timestamp="2025-07-02T10:32:34.787Z" hostname="Ajayk" tests="9" failures="0" errors="0" skipped="0" time="0.131564">
        <testcase classname="src/ui/hooks/useConsoleMessages.test.ts" name="useConsoleMessages &gt; should initialize with an empty array of console messages" time="0.0440169">
        </testcase>
        <testcase classname="src/ui/hooks/useConsoleMessages.test.ts" name="useConsoleMessages &gt; should add a new message" time="0.0111601">
        </testcase>
        <testcase classname="src/ui/hooks/useConsoleMessages.test.ts" name="useConsoleMessages &gt; should consolidate identical consecutive messages" time="0.0085508">
        </testcase>
        <testcase classname="src/ui/hooks/useConsoleMessages.test.ts" name="useConsoleMessages &gt; should not consolidate different messages" time="0.0082657">
        </testcase>
        <testcase classname="src/ui/hooks/useConsoleMessages.test.ts" name="useConsoleMessages &gt; should not consolidate messages if type is different" time="0.0087016">
        </testcase>
        <testcase classname="src/ui/hooks/useConsoleMessages.test.ts" name="useConsoleMessages &gt; should clear console messages" time="0.0091612">
        </testcase>
        <testcase classname="src/ui/hooks/useConsoleMessages.test.ts" name="useConsoleMessages &gt; should clear pending timeout on clearConsoleMessages" time="0.007638">
        </testcase>
        <testcase classname="src/ui/hooks/useConsoleMessages.test.ts" name="useConsoleMessages &gt; should clear message queue on clearConsoleMessages" time="0.0116795">
        </testcase>
        <testcase classname="src/ui/hooks/useConsoleMessages.test.ts" name="useConsoleMessages &gt; should cleanup timeout on unmount" time="0.0166199">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/useEditorSettings.test.ts" timestamp="2025-07-02T10:32:34.789Z" hostname="Ajayk" tests="10" failures="0" errors="0" skipped="0" time="0.1470697">
        <testcase classname="src/ui/hooks/useEditorSettings.test.ts" name="useEditorSettings &gt; should initialize with dialog closed" time="0.0460802">
        </testcase>
        <testcase classname="src/ui/hooks/useEditorSettings.test.ts" name="useEditorSettings &gt; should open editor dialog when openEditorDialog is called" time="0.009366">
        </testcase>
        <testcase classname="src/ui/hooks/useEditorSettings.test.ts" name="useEditorSettings &gt; should close editor dialog when exitEditorDialog is called" time="0.008018">
        </testcase>
        <testcase classname="src/ui/hooks/useEditorSettings.test.ts" name="useEditorSettings &gt; should handle editor selection successfully" time="0.0261414">
        </testcase>
        <testcase classname="src/ui/hooks/useEditorSettings.test.ts" name="useEditorSettings &gt; should handle clearing editor preference (undefined editor)" time="0.0087628">
        </testcase>
        <testcase classname="src/ui/hooks/useEditorSettings.test.ts" name="useEditorSettings &gt; should handle different editor types" time="0.0066951">
        </testcase>
        <testcase classname="src/ui/hooks/useEditorSettings.test.ts" name="useEditorSettings &gt; should handle different setting scopes" time="0.0071371">
        </testcase>
        <testcase classname="src/ui/hooks/useEditorSettings.test.ts" name="useEditorSettings &gt; should not set preference for unavailable editors" time="0.0061939">
        </testcase>
        <testcase classname="src/ui/hooks/useEditorSettings.test.ts" name="useEditorSettings &gt; should not set preference for editors not allowed in sandbox" time="0.0166814">
        </testcase>
        <testcase classname="src/ui/hooks/useEditorSettings.test.ts" name="useEditorSettings &gt; should handle errors during editor selection" time="0.0062572">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/useGitBranchName.test.ts" timestamp="2025-07-02T10:32:34.792Z" hostname="Ajayk" tests="7" failures="0" errors="0" skipped="2" time="0.1726993">
        <testcase classname="src/ui/hooks/useGitBranchName.test.ts" name="useGitBranchName &gt; should return branch name" time="0.0810129">
        </testcase>
        <testcase classname="src/ui/hooks/useGitBranchName.test.ts" name="useGitBranchName &gt; should return undefined if git command fails" time="0.0197051">
        </testcase>
        <testcase classname="src/ui/hooks/useGitBranchName.test.ts" name="useGitBranchName &gt; should return short commit hash if branch is HEAD (detached state)" time="0.0282168">
        </testcase>
        <testcase classname="src/ui/hooks/useGitBranchName.test.ts" name="useGitBranchName &gt; should return undefined if branch is HEAD and getting commit hash fails" time="0.0164339">
        </testcase>
        <testcase classname="src/ui/hooks/useGitBranchName.test.ts" name="useGitBranchName &gt; should update branch name when .git/HEAD changes" time="0.0026905">
            <skipped/>
        </testcase>
        <testcase classname="src/ui/hooks/useGitBranchName.test.ts" name="useGitBranchName &gt; should handle watcher setup error silently" time="0.0142595">
        </testcase>
        <testcase classname="src/ui/hooks/useGitBranchName.test.ts" name="useGitBranchName &gt; should cleanup watcher on unmount" time="0.0053939">
            <skipped/>
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/useHistoryManager.test.ts" timestamp="2025-07-02T10:32:34.794Z" hostname="Ajayk" tests="11" failures="1" errors="0" skipped="0" time="0.1932632">
        <testcase classname="src/ui/hooks/useHistoryManager.test.ts" name="useHistoryManager &gt; should initialize with an empty history" time="0.0453272">
        </testcase>
        <testcase classname="src/ui/hooks/useHistoryManager.test.ts" name="useHistoryManager &gt; should add an item to history with a unique ID" time="0.0145718">
        </testcase>
        <testcase classname="src/ui/hooks/useHistoryManager.test.ts" name="useHistoryManager &gt; should generate unique IDs for items added with the same base timestamp" time="0.007975">
        </testcase>
        <testcase classname="src/ui/hooks/useHistoryManager.test.ts" name="useHistoryManager &gt; should update an existing history item" time="0.0159995">
        </testcase>
        <testcase classname="src/ui/hooks/useHistoryManager.test.ts" name="useHistoryManager &gt; should not change history if updateHistoryItem is called with a non-existent ID" time="0.0190453">
        </testcase>
        <testcase classname="src/ui/hooks/useHistoryManager.test.ts" name="useHistoryManager &gt; should clear the history" time="0.0153062">
        </testcase>
        <testcase classname="src/ui/hooks/useHistoryManager.test.ts" name="useHistoryManager &gt; should not add consecutive duplicate user messages" time="0.0088367">
        </testcase>
        <testcase classname="src/ui/hooks/useHistoryManager.test.ts" name="useHistoryManager &gt; should add duplicate user messages if they are not consecutive" time="0.0375221">
            <failure message="expected [ { type: &apos;user&apos;, …(2) }, …(1) ] to have a length of 3 but got 2" type="AssertionError">
AssertionError: expected [ { type: &apos;user&apos;, …(2) }, …(1) ] to have a length of 3 but got 2

- Expected
+ Received

- 3
+ 2

 ❯ src/ui/hooks/useHistoryManager.test.ts:197:36
            </failure>
        </testcase>
        <testcase classname="src/ui/hooks/useHistoryManager.test.ts" name="useHistoryManager &gt; should not add consecutive duplicate arien messages" time="0.008488">
        </testcase>
        <testcase classname="src/ui/hooks/useHistoryManager.test.ts" name="useHistoryManager &gt; should not add consecutive duplicate arien_content messages" time="0.0052338">
        </testcase>
        <testcase classname="src/ui/hooks/useHistoryManager.test.ts" name="useHistoryManager &gt; should not add duplicate mixed arien/arien_content messages" time="0.0091263">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/useInputHistory.test.ts" timestamp="2025-07-02T10:32:34.798Z" hostname="Ajayk" tests="11" failures="0" errors="0" skipped="0" time="0.1287517">
        <testcase classname="src/ui/hooks/useInputHistory.test.ts" name="useInputHistory &gt; should initialize with historyIndex -1 and empty originalQueryBeforeNav" time="0.0465577">
        </testcase>
        <testcase classname="src/ui/hooks/useInputHistory.test.ts" name="useInputHistory &gt; handleSubmit &gt; should call onSubmit with trimmed value and reset history" time="0.0108588">
        </testcase>
        <testcase classname="src/ui/hooks/useInputHistory.test.ts" name="useInputHistory &gt; handleSubmit &gt; should not call onSubmit if value is empty after trimming" time="0.0059094">
        </testcase>
        <testcase classname="src/ui/hooks/useInputHistory.test.ts" name="useInputHistory &gt; navigateUp &gt; should not navigate if isActive is false" time="0.0072936">
        </testcase>
        <testcase classname="src/ui/hooks/useInputHistory.test.ts" name="useInputHistory &gt; navigateUp &gt; should not navigate if userMessages is empty" time="0.0051003">
        </testcase>
        <testcase classname="src/ui/hooks/useInputHistory.test.ts" name="useInputHistory &gt; navigateUp &gt; should call onChange with the last message when navigating up from initial state" time="0.0104051">
        </testcase>
        <testcase classname="src/ui/hooks/useInputHistory.test.ts" name="useInputHistory &gt; navigateUp &gt; should store currentQuery as originalQueryBeforeNav on first navigateUp" time="0.0069897">
        </testcase>
        <testcase classname="src/ui/hooks/useInputHistory.test.ts" name="useInputHistory &gt; navigateUp &gt; should navigate through history messages on subsequent navigateUp calls" time="0.0061294">
        </testcase>
        <testcase classname="src/ui/hooks/useInputHistory.test.ts" name="useInputHistory &gt; navigateDown &gt; should not navigate if isActive is false" time="0.0070953">
        </testcase>
        <testcase classname="src/ui/hooks/useInputHistory.test.ts" name="useInputHistory &gt; navigateDown &gt; should not navigate if historyIndex is -1 (not in history navigation)" time="0.0104743">
        </testcase>
        <testcase classname="src/ui/hooks/useInputHistory.test.ts" name="useInputHistory &gt; navigateDown &gt; should restore originalQueryBeforeNav when navigating down to initial state" time="0.005826">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/useLoadingIndicator.test.ts" timestamp="2025-07-02T10:32:34.801Z" hostname="Ajayk" tests="5" failures="0" errors="0" skipped="0" time="0.123853">
        <testcase classname="src/ui/hooks/useLoadingIndicator.test.ts" name="useLoadingIndicator &gt; should initialize with default values when Idle" time="0.0520002">
        </testcase>
        <testcase classname="src/ui/hooks/useLoadingIndicator.test.ts" name="useLoadingIndicator &gt; should reflect values when Responding" time="0.0196172">
        </testcase>
        <testcase classname="src/ui/hooks/useLoadingIndicator.test.ts" name="useLoadingIndicator &gt; should show waiting phrase and retain elapsedTime when WaitingForConfirmation" time="0.0138274">
        </testcase>
        <testcase classname="src/ui/hooks/useLoadingIndicator.test.ts" name="useLoadingIndicator &gt; should reset elapsedTime and use a witty phrase when transitioning from WaitingForConfirmation to Responding" time="0.0147643">
        </testcase>
        <testcase classname="src/ui/hooks/useLoadingIndicator.test.ts" name="useLoadingIndicator &gt; should reset timer and phrase when streamingState changes from Responding to Idle" time="0.0186361">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/usePhraseCycler.test.ts" timestamp="2025-07-02T10:32:34.802Z" hostname="Ajayk" tests="7" failures="0" errors="0" skipped="0" time="0.1571131">
        <testcase classname="src/ui/hooks/usePhraseCycler.test.ts" name="usePhraseCycler &gt; should initialize with the first witty phrase when not active and not waiting" time="0.0500113">
        </testcase>
        <testcase classname="src/ui/hooks/usePhraseCycler.test.ts" name="usePhraseCycler &gt; should show &quot;Waiting for user confirmation...&quot; when isWaiting is true" time="0.0120804">
        </testcase>
        <testcase classname="src/ui/hooks/usePhraseCycler.test.ts" name="usePhraseCycler &gt; should not cycle phrases if isActive is false and not waiting" time="0.0277435">
        </testcase>
        <testcase classname="src/ui/hooks/usePhraseCycler.test.ts" name="usePhraseCycler &gt; should cycle through witty phrases when isActive is true and not waiting" time="0.0340847">
        </testcase>
        <testcase classname="src/ui/hooks/usePhraseCycler.test.ts" name="usePhraseCycler &gt; should reset to a witty phrase when isActive becomes true after being false (and not waiting)" time="0.0156191">
        </testcase>
        <testcase classname="src/ui/hooks/usePhraseCycler.test.ts" name="usePhraseCycler &gt; should clear phrase interval on unmount when active" time="0.0050297">
        </testcase>
        <testcase classname="src/ui/hooks/usePhraseCycler.test.ts" name="usePhraseCycler &gt; should reset to a witty phrase when transitioning from waiting to active" time="0.0077848">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/useShellHistory.test.ts" timestamp="2025-07-02T10:32:34.804Z" hostname="Ajayk" tests="7" failures="0" errors="0" skipped="0" time="0.9941624">
        <testcase classname="src/ui/hooks/useShellHistory.test.ts" name="useShellHistory &gt; should initialize and read the history file from the correct path" time="0.1628681">
        </testcase>
        <testcase classname="src/ui/hooks/useShellHistory.test.ts" name="useShellHistory &gt; should handle a non-existent history file gracefully" time="0.0997725">
        </testcase>
        <testcase classname="src/ui/hooks/useShellHistory.test.ts" name="useShellHistory &gt; should add a command and write to the history file" time="0.1468268">
        </testcase>
        <testcase classname="src/ui/hooks/useShellHistory.test.ts" name="useShellHistory &gt; should navigate history correctly with previous/next commands" time="0.07819">
        </testcase>
        <testcase classname="src/ui/hooks/useShellHistory.test.ts" name="useShellHistory &gt; should not add empty or whitespace-only commands to history" time="0.0946323">
        </testcase>
        <testcase classname="src/ui/hooks/useShellHistory.test.ts" name="useShellHistory &gt; should truncate history to MAX_HISTORY_LENGTH (100)" time="0.1608564">
        </testcase>
        <testcase classname="src/ui/hooks/useShellHistory.test.ts" name="useShellHistory &gt; should move an existing command to the top when re-added" time="0.2452834">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/useTimer.test.ts" timestamp="2025-07-02T10:32:34.806Z" hostname="Ajayk" tests="8" failures="0" errors="0" skipped="0" time="0.1854537">
        <testcase classname="src/ui/hooks/useTimer.test.ts" name="useTimer &gt; should initialize with 0" time="0.0720455">
        </testcase>
        <testcase classname="src/ui/hooks/useTimer.test.ts" name="useTimer &gt; should not increment time if isActive is false" time="0.0351966">
        </testcase>
        <testcase classname="src/ui/hooks/useTimer.test.ts" name="useTimer &gt; should increment time every second if isActive is true" time="0.025115">
        </testcase>
        <testcase classname="src/ui/hooks/useTimer.test.ts" name="useTimer &gt; should reset to 0 and start incrementing when isActive becomes true from false" time="0.0074046">
        </testcase>
        <testcase classname="src/ui/hooks/useTimer.test.ts" name="useTimer &gt; should reset to 0 when resetKey changes while active" time="0.0081151">
        </testcase>
        <testcase classname="src/ui/hooks/useTimer.test.ts" name="useTimer &gt; should be 0 if isActive is false, regardless of resetKey changes" time="0.0109043">
        </testcase>
        <testcase classname="src/ui/hooks/useTimer.test.ts" name="useTimer &gt; should clear timer on unmount" time="0.0081209">
        </testcase>
        <testcase classname="src/ui/hooks/useTimer.test.ts" name="useTimer &gt; should preserve elapsedTime when isActive becomes false, and reset to 0 when it becomes active again" time="0.0080829">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/useToolScheduler.test.ts" timestamp="2025-07-02T10:32:34.808Z" hostname="Ajayk" tests="21" failures="0" errors="0" skipped="4" time="0.323977">
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="useReactToolScheduler in YOLO Mode &gt; should skip confirmation and execute tool directly when yoloMode is true" time="0.2222527">
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="useReactToolScheduler &gt; initial state should be empty" time="0.0081722">
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="useReactToolScheduler &gt; should schedule and execute a tool call successfully" time="0.0180423">
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="useReactToolScheduler &gt; should handle tool not found" time="0.0122878">
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="useReactToolScheduler &gt; should handle error during shouldConfirmExecute" time="0.0110296">
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="useReactToolScheduler &gt; should handle error during execute" time="0.0146596">
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="useReactToolScheduler &gt; should handle tool requiring confirmation - approved" time="0">
            <skipped/>
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="useReactToolScheduler &gt; should handle tool requiring confirmation - cancelled by user" time="0">
            <skipped/>
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="useReactToolScheduler &gt; should handle live output updates" time="0">
            <skipped/>
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="useReactToolScheduler &gt; should schedule and execute multiple tool calls" time="0.0183882">
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="useReactToolScheduler &gt; should throw error if scheduling while already running" time="0">
            <skipped/>
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="mapToDisplay &gt; should map ToolCall with status &apos;validating&apos; (validating) correctly" time="0.0035021">
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="mapToDisplay &gt; should map ToolCall with status &apos;awaiting_approval&apos; (awaiting_approval) correctly" time="0.0019779">
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="mapToDisplay &gt; should map ToolCall with status &apos;scheduled&apos; (scheduled) correctly" time="0.0016236">
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="mapToDisplay &gt; should map ToolCall with status &apos;executing&apos; (executing no live output) correctly" time="0.0007591">
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="mapToDisplay &gt; should map ToolCall with status &apos;executing&apos; (executing with live output) correctly" time="0.0006332">
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="mapToDisplay &gt; should map ToolCall with status &apos;success&apos; (success) correctly" time="0.0007318">
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="mapToDisplay &gt; should map ToolCall with status &apos;error&apos; (error tool not found) correctly" time="0.0008243">
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="mapToDisplay &gt; should map ToolCall with status &apos;error&apos; (error tool execution failed) correctly" time="0.0006528">
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="mapToDisplay &gt; should map ToolCall with status &apos;cancelled&apos; (cancelled) correctly" time="0.0009539">
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="mapToDisplay &gt; should map an array of ToolCalls correctly" time="0.0017633">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/components/messages/ArienMessage.test.tsx" timestamp="2025-07-02T10:32:34.814Z" hostname="Ajayk" tests="6" failures="1" errors="0" skipped="0" time="0.2275324">
        <testcase classname="src/ui/components/messages/ArienMessage.test.tsx" name="ArienMessage &gt; should render without crashing" time="0.1090901">
        </testcase>
        <testcase classname="src/ui/components/messages/ArienMessage.test.tsx" name="ArienMessage &gt; should display message text in MarkdownDisplay" time="0.0256062">
        </testcase>
        <testcase classname="src/ui/components/messages/ArienMessage.test.tsx" name="ArienMessage &gt; should show animated icon with pending state" time="0.0200242">
        </testcase>
        <testcase classname="src/ui/components/messages/ArienMessage.test.tsx" name="ArienMessage &gt; should show animated icon with non-pending state" time="0.0453348">
            <failure message="expected &apos;🤖&apos; to be &apos;&apos; // Object.is equality" type="AssertionError">
AssertionError: expected &apos;🤖&apos; to be &apos;&apos; // Object.is equality

- Expected
+ Received

+ 🤖

 ❯ src/ui/components/messages/ArienMessage.test.tsx:72:38
            </failure>
        </testcase>
        <testcase classname="src/ui/components/messages/ArienMessage.test.tsx" name="ArienMessage &gt; should pass availableTerminalHeight to MarkdownDisplay when provided" time="0.0137634">
        </testcase>
        <testcase classname="src/ui/components/messages/ArienMessage.test.tsx" name="ArienMessage &gt; should pass terminalWidth to MarkdownDisplay" time="0.0085715">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/components/messages/DiffRenderer.test.tsx" timestamp="2025-07-02T10:32:34.817Z" hostname="Ajayk" tests="13" failures="0" errors="0" skipped="0" time="0.8539886">
        <testcase classname="src/ui/components/messages/DiffRenderer.test.tsx" name="&lt;OverflowProvider&gt;&lt;DiffRenderer /&gt;&lt;/OverflowProvider&gt; &gt; should call colorizeCode with correct language for new file with known extension" time="0.0957964">
        </testcase>
        <testcase classname="src/ui/components/messages/DiffRenderer.test.tsx" name="&lt;OverflowProvider&gt;&lt;DiffRenderer /&gt;&lt;/OverflowProvider&gt; &gt; should call colorizeCode with null language for new file with unknown extension" time="0.4176727">
        </testcase>
        <testcase classname="src/ui/components/messages/DiffRenderer.test.tsx" name="&lt;OverflowProvider&gt;&lt;DiffRenderer /&gt;&lt;/OverflowProvider&gt; &gt; should call colorizeCode with null language for new file if no filename is provided" time="0.0382403">
        </testcase>
        <testcase classname="src/ui/components/messages/DiffRenderer.test.tsx" name="&lt;OverflowProvider&gt;&lt;DiffRenderer /&gt;&lt;/OverflowProvider&gt; &gt; should render diff content for existing file (not calling colorizeCode directly for the whole block)" time="0.0298582">
        </testcase>
        <testcase classname="src/ui/components/messages/DiffRenderer.test.tsx" name="&lt;OverflowProvider&gt;&lt;DiffRenderer /&gt;&lt;/OverflowProvider&gt; &gt; should handle diff with only header and no changes" time="0.0064342">
        </testcase>
        <testcase classname="src/ui/components/messages/DiffRenderer.test.tsx" name="&lt;OverflowProvider&gt;&lt;DiffRenderer /&gt;&lt;/OverflowProvider&gt; &gt; should handle empty diff content" time="0.0040736">
        </testcase>
        <testcase classname="src/ui/components/messages/DiffRenderer.test.tsx" name="&lt;OverflowProvider&gt;&lt;DiffRenderer /&gt;&lt;/OverflowProvider&gt; &gt; should render a gap indicator for skipped lines" time="0.0297328">
        </testcase>
        <testcase classname="src/ui/components/messages/DiffRenderer.test.tsx" name="&lt;OverflowProvider&gt;&lt;DiffRenderer /&gt;&lt;/OverflowProvider&gt; &gt; should not render a gap indicator for small gaps (&lt;= MAX_CONTEXT_LINES_WITHOUT_GAP)" time="0.0404222">
        </testcase>
        <testcase classname="src/ui/components/messages/DiffRenderer.test.tsx" name="&lt;OverflowProvider&gt;&lt;DiffRenderer /&gt;&lt;/OverflowProvider&gt; &gt; should correctly render a diff with multiple hunks and a gap indicator &gt; with terminalWidth 80 and height undefined" time="0.0356971">
        </testcase>
        <testcase classname="src/ui/components/messages/DiffRenderer.test.tsx" name="&lt;OverflowProvider&gt;&lt;DiffRenderer /&gt;&lt;/OverflowProvider&gt; &gt; should correctly render a diff with multiple hunks and a gap indicator &gt; with terminalWidth 80 and height 6" time="0.0432936">
        </testcase>
        <testcase classname="src/ui/components/messages/DiffRenderer.test.tsx" name="&lt;OverflowProvider&gt;&lt;DiffRenderer /&gt;&lt;/OverflowProvider&gt; &gt; should correctly render a diff with multiple hunks and a gap indicator &gt; with terminalWidth 30 and height 6" time="0.0256977">
        </testcase>
        <testcase classname="src/ui/components/messages/DiffRenderer.test.tsx" name="&lt;OverflowProvider&gt;&lt;DiffRenderer /&gt;&lt;/OverflowProvider&gt; &gt; should correctly render a diff with a SVN diff format" time="0.0347569">
        </testcase>
        <testcase classname="src/ui/components/messages/DiffRenderer.test.tsx" name="&lt;OverflowProvider&gt;&lt;DiffRenderer /&gt;&lt;/OverflowProvider&gt; &gt; should correctly render a new file with no file extension correctly" time="0.0456355">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/components/messages/ToolConfirmationMessage.test.tsx" timestamp="2025-07-02T10:32:34.820Z" hostname="Ajayk" tests="2" failures="0" errors="0" skipped="0" time="0.0769691">
        <testcase classname="src/ui/components/messages/ToolConfirmationMessage.test.tsx" name="ToolConfirmationMessage &gt; should not display urls if prompt and url are the same" time="0.0600725">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolConfirmationMessage.test.tsx" name="ToolConfirmationMessage &gt; should display urls if prompt and url are different" time="0.0144881">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/components/messages/ToolMessage.test.tsx" timestamp="2025-07-02T10:32:34.821Z" hostname="Ajayk" tests="16" failures="0" errors="0" skipped="0" time="0.2647782">
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; renders basic tool information" time="0.1164435">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; ToolStatusIndicator rendering &gt; shows ✓ for Success status" time="0.0129728">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; ToolStatusIndicator rendering &gt; shows ◦ for Pending status" time="0.0088631">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; ToolStatusIndicator rendering &gt; shows ⚡ for Confirming status" time="0.0099326">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; ToolStatusIndicator rendering &gt; shows ⊘ for Canceled status" time="0.0098802">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; ToolStatusIndicator rendering &gt; shows ✗ for Error status" time="0.0079665">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; ToolStatusIndicator rendering &gt; shows executing symbol for Executing status when streamingState is Idle" time="0.0103562">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; ToolStatusIndicator rendering &gt; shows executing symbol for Executing status when streamingState is WaitingForConfirmation" time="0.0072342">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; ToolStatusIndicator rendering &gt; shows MockRespondingSpinner for Executing status when streamingState is Responding" time="0.0097259">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; renders DiffRenderer for diff results" time="0.0093595">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; renders emphasis correctly" time="0.0141765">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; handles long result display by truncating" time="0.0074198">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; renders without result display" time="0.0073227">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; respects availableTerminalHeight constraint" time="0.0122282">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; handles empty result display" time="0.0039285">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; handles different terminal widths" time="0.010536">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/components/shared/MaxSizedBox.test.tsx" timestamp="2025-07-02T10:32:34.825Z" hostname="Ajayk" tests="16" failures="0" errors="0" skipped="0" time="0.2863111">
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; renders children without truncation when they fit" time="0.1174689">
            <system-err>
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;

            </system-err>
        </testcase>
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; hides lines when content exceeds maxHeight" time="0.0121692">
            <system-err>
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;

            </system-err>
        </testcase>
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; hides lines at the end when content exceeds maxHeight and overflowDirection is bottom" time="0.0165332">
            <system-err>
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;

            </system-err>
        </testcase>
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; wraps text that exceeds maxWidth" time="0.0124561">
            <system-err>
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;

            </system-err>
        </testcase>
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; handles mixed wrapping and non-wrapping segments" time="0.0240031">
            <system-err>
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;

            </system-err>
        </testcase>
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; handles words longer than maxWidth by splitting them" time="0.0124917">
            <system-err>
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;

            </system-err>
        </testcase>
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; does not truncate when maxHeight is undefined" time="0.0060947">
            <system-err>
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;

            </system-err>
        </testcase>
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; shows plural &quot;lines&quot; when more than one line is hidden" time="0.0059245">
            <system-err>
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;

            </system-err>
        </testcase>
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; shows plural &quot;lines&quot; when more than one line is hidden and overflowDirection is bottom" time="0.0058456">
            <system-err>
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;

            </system-err>
        </testcase>
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; renders an empty box for empty children" time="0.0028008">
        </testcase>
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; wraps text with multi-byte unicode characters correctly" time="0.0058939">
            <system-err>
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;

            </system-err>
        </testcase>
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; wraps text with multi-byte emoji characters correctly" time="0.0061305">
            <system-err>
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;

            </system-err>
        </testcase>
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; accounts for additionalHiddenLinesCount" time="0.0066281">
            <system-err>
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;

            </system-err>
        </testcase>
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; handles React.Fragment as a child" time="0.0076334">
            <system-err>
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;

            </system-err>
        </testcase>
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; clips a long single text child from the top" time="0.0165448">
            <system-err>
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;

            </system-err>
        </testcase>
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; clips a long single text child from the bottom" time="0.0138004">
            <system-err>
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;

            </system-err>
        </testcase>
    </testsuite>
    <testsuite name="src/ui/components/shared/text-buffer.test.ts" timestamp="2025-07-02T10:32:34.832Z" hostname="Ajayk" tests="60" failures="0" errors="0" skipped="0" time="0.7318656">
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Initialization &gt; should initialize with empty text and cursor at (0,0) by default" time="0.0634771">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Initialization &gt; should initialize with provided initialText" time="0.0119221">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Initialization &gt; should initialize with initialText and initialCursorOffset" time="0.0101246">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Initialization &gt; should wrap visual lines" time="0.0144119">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Initialization &gt; should wrap visual lines with multiple spaces" time="0.0103312">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Initialization &gt; should wrap visual lines even without spaces" time="0.0053463">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Initialization &gt; should initialize with multi-byte unicode characters and correct cursor offset" time="0.0067776">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Basic Editing &gt; insert: should insert a character and update cursor" time="0.0111081">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Basic Editing &gt; insert: should insert text in the middle of a line" time="0.0113509">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Basic Editing &gt; newline: should create a new line and move cursor" time="0.0156965">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Basic Editing &gt; backspace: should delete char to the left or merge lines" time="0.0190014">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Basic Editing &gt; del: should delete char to the right or merge lines" time="0.0171791">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Drag and Drop File Paths &gt; should prepend @ to a valid file path on insert" time="0.0074983">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Drag and Drop File Paths &gt; should not prepend @ to an invalid file path on insert" time="0.0068749">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Drag and Drop File Paths &gt; should handle quoted paths" time="0.01303">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Drag and Drop File Paths &gt; should not prepend @ to short text that is not a path" time="0.0105036">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Cursor Movement &gt; move: left/right should work within and across visual lines (due to wrapping)" time="0.0217526">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Cursor Movement &gt; move: up/down should preserve preferred visual column" time="0.0164828">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Cursor Movement &gt; move: home/end should go to visual line start/end" time="0.0114212">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Visual Layout &amp; Viewport &gt; should wrap long lines correctly into visualLines" time="0.0078258">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Visual Layout &amp; Viewport &gt; should update visualScrollRow when visualCursor moves out of viewport" time="0.023394">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Undo/Redo &gt; should undo and redo an insert operation" time="0.0089052">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Undo/Redo &gt; should undo and redo a newline operation" time="0.0092284">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Unicode Handling &gt; insert: should correctly handle multi-byte unicode characters" time="0.0053481">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Unicode Handling &gt; backspace: should correctly delete multi-byte unicode characters" time="0.0072227">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Unicode Handling &gt; move: left/right should treat multi-byte chars as single units for visual cursor" time="0.0081286">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; handleInput &gt; should insert printable characters" time="0.0128316">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; handleInput &gt; should handle &quot;Enter&quot; key as newline" time="0.0067691">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; handleInput &gt; should handle &quot;Backspace&quot; key" time="0.0076622">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; handleInput &gt; should handle multiple delete characters in one input" time="0.0100968">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; handleInput &gt; should handle inserts that contain delete characters " time="0.0078699">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; handleInput &gt; should handle inserts with a mix of regular and delete characters " time="0.0129694">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; handleInput &gt; should handle arrow keys for movement" time="0.0082922">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; handleInput &gt; should strip ANSI escape codes when pasting text" time="0.0067254">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; handleInput &gt; should handle VSCode terminal Shift+Enter as newline" time="0.0057099">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; handleInput &gt; should correctly handle repeated pasting of long text" time="0.1666031">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; replaceRange &gt; should replace a single-line range with single-line text" time="0.0095631">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; replaceRange &gt; should replace a multi-line range with single-line text" time="0.0065244">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; replaceRange &gt; should delete a range when replacing with an empty string" time="0.0058915">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; replaceRange &gt; should handle replacing at the beginning of the text" time="0.0065271">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; replaceRange &gt; should handle replacing at the end of the text" time="0.027241">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; replaceRange &gt; should handle replacing the entire buffer content" time="0.0053537">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; replaceRange &gt; should correctly replace with unicode characters" time="0.0067897">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; replaceRange &gt; should handle invalid range by returning false and not changing text" time="0.0106124">
            <system-err>
Invalid range provided to replaceRange {
  startRow: [33m0[39m,
  startCol: [33m5[39m,
  endRow: [33m0[39m,
  endCol: [33m3[39m,
  linesLength: [33m1[39m,
  endRowLineLength: [33m4[39m
}
Invalid range provided to replaceRange {
  startRow: [33m1[39m,
  startCol: [33m0[39m,
  endRow: [33m0[39m,
  endCol: [33m0[39m,
  linesLength: [33m1[39m,
  endRowLineLength: [33m4[39m
}

            </system-err>
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; replaceRange &gt; replaceRange: multiple lines with a single character" time="0.0082389">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Input Sanitization &gt; should strip ANSI escape codes from input" time="0.0058531">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Input Sanitization &gt; should strip control characters from input" time="0.0055198">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Input Sanitization &gt; should strip mixed ANSI and control characters from input" time="0.0062856">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Input Sanitization &gt; should not strip standard characters or newlines" time="0.0062404">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Input Sanitization &gt; should sanitize pasted text via handleInput" time="0.0048208">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="offsetToLogicalPos &gt; should return [0,0] for offset 0" time="0.000603">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="offsetToLogicalPos &gt; should handle single line text" time="0.0007062">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="offsetToLogicalPos &gt; should handle multi-line text" time="0.0017935">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="offsetToLogicalPos &gt; should handle empty lines" time="0.0012355">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="offsetToLogicalPos &gt; should handle text ending with a newline" time="0.0008176">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="offsetToLogicalPos &gt; should handle text starting with a newline" time="0.0006839">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="offsetToLogicalPos &gt; should handle empty string input" time="0.0004721">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="offsetToLogicalPos &gt; should handle multi-byte unicode characters correctly" time="0.0008937">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="offsetToLogicalPos &gt; should handle offset exactly at newline character" time="0.0004936">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="offsetToLogicalPos &gt; should handle offset in the middle of a multi-byte character (should place at start of that char)" time="0.0005793">
        </testcase>
    </testsuite>
</testsuites>
