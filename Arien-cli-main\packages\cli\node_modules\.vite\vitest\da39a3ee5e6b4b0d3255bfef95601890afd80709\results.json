{"version": "3.2.4", "results": [[":src/ui/hooks/slashCommandProcessor.test.ts", {"duration": 579.9567999999999, "failed": true}], [":src/ui/components/shared/text-buffer.test.ts", {"duration": 731.8655999999992, "failed": false}], [":src/ui/hooks/useArienStream.test.tsx", {"duration": 633.3179999999993, "failed": false}], [":src/ui/hooks/useToolScheduler.test.ts", {"duration": 323.97700000000077, "failed": false}], [":src/ui/hooks/atCommandProcessor.test.ts", {"duration": 123.43329999999878, "failed": true}], [":src/config/settings.test.ts", {"duration": 80.08889999999883, "failed": false}], [":src/config/config.test.ts", {"duration": 83634.98819999999, "failed": true}], [":src/ui/App.test.tsx", {"duration": 1382.0299999999988, "failed": true}], [":src/ui/hooks/useCompletion.integration.test.ts", {"duration": 1586.0082000000002, "failed": true}], [":src/ui/components/messages/DiffRenderer.test.tsx", {"duration": 853.9885999999997, "failed": false}], [":src/ui/hooks/useAutoAcceptIndicator.test.ts", {"duration": 102.03620000000046, "failed": false}], [":src/ui/components/shared/MaxSizedBox.test.tsx", {"duration": 286.3110999999999, "failed": false}], [":src/nonInteractiveCli.test.ts", {"duration": 38.3695000000007, "failed": false}], [":src/ui/components/messages/ToolMessage.test.tsx", {"duration": 264.77820000000065, "failed": false}], [":src/ui/hooks/useHistoryManager.test.ts", {"duration": 193.26320000000032, "failed": true}], [":src/config/mcp-integration.test.ts", {"duration": 21305.6416, "failed": true}], [":src/ui/hooks/useEditorSettings.test.ts", {"duration": 147.0697, "failed": false}], [":src/ui/contexts/SessionContext.test.tsx", {"duration": 144.3707999999997, "failed": false}], [":src/ui/hooks/useInputHistory.test.ts", {"duration": 128.7516999999998, "failed": false}], [":src/ui/hooks/useGitBranchName.test.ts", {"duration": 172.69930000000022, "failed": false}], [":src/config/config.integration.test.ts", {"duration": 349.5011999999997, "failed": false}], [":src/ui/hooks/useShellHistory.test.ts", {"duration": 994.1623999999993, "failed": false}], [":src/config/built-in-mcp-servers.test.ts", {"duration": 67.72149999999965, "failed": true}], [":src/ui/components/LoadingIndicator.test.tsx", {"duration": 216.96419999999944, "failed": false}], [":src/ui/components/InputPrompt.test.tsx", {"duration": 975.0610000000015, "failed": false}], [":src/ui/hooks/useConsoleMessages.test.ts", {"duration": 131.56399999999985, "failed": false}], [":src/ui/hooks/usePhraseCycler.test.ts", {"duration": 157.11309999999958, "failed": false}], [":src/ui/hooks/shellCommandProcessor.test.ts", {"duration": 111.98540000000048, "failed": false}], [":src/ui/utils/errorParsing.test.ts", {"duration": 15.12039999999979, "failed": false}], [":src/ui/hooks/useLoadingIndicator.test.ts", {"duration": 123.85300000000007, "failed": false}], [":src/arien.test.tsx", {"duration": 16.782900000000154, "failed": false}], [":src/ui/hooks/useTimer.test.ts", {"duration": 185.45370000000003, "failed": false}], [":src/config/extension.test.ts", {"duration": 53.751899999999296, "failed": false}], [":src/ui/components/AuthDialog.test.tsx", {"duration": 410.54279999999926, "failed": false}], [":src/ui/components/messages/ArienMessage.test.tsx", {"duration": 227.53240000000005, "failed": true}], [":src/ui/components/HistoryItemDisplay.test.tsx", {"duration": 352.78470000000016, "failed": true}], [":src/utils/startupWarnings.test.ts", {"duration": 0, "failed": false}], [":src/ui/components/Stats.test.tsx", {"duration": 232.79009999999926, "failed": false}], [":src/ui/utils/formatters.test.ts", {"duration": 18.115999999999985, "failed": false}], [":src/ui/utils/markdownUtilities.test.ts", {"duration": 10.782300000000305, "failed": false}], [":src/ui/components/StatsDisplay.test.tsx", {"duration": 158.2511999999997, "failed": false}], [":src/ui/components/messages/ToolConfirmationMessage.test.tsx", {"duration": 76.96909999999934, "failed": false}], [":src/ui/components/SessionSummaryDisplay.test.tsx", {"duration": 162.23739999999998, "failed": false}], [":src/ui/utils/textUtils.test.ts", {"duration": 11.07049999999981, "failed": false}]]}